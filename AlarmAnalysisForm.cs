﻿// {{RIPER-5:
//   Action: "Modified"
//   Task_ID: "#npMQt8z5TQVKNmJKEdaGpJ"
//   Timestamp: "2025-08-24T16:12:36+08:00"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "Ribbon界面重构完成，包含所有必需的按钮和控件。"
// }}
// {{START_MODIFICATIONS}}

using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AlarmAnalysis.Services;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Common;

namespace AlarmAnalysis
{
    /// <summary>
    /// 报警分析主窗口
    /// </summary>
    public partial class AlarmAnalysisForm : DevExpress.XtraBars.Ribbon.RibbonForm
    {
        #region 私有字段

        private AlarmAnalysisService _analysisService;
        private bool _isAnalysisRunning = false;

        // 分析结果窗体
        private Form _frequencyAnalysisForm;
        private Form _timeAnalysisForm;
        private Form _advancedAnalysisForm;
        private Form _trendAnalysisForm;

        // 分析控件
        private FrequencyAnalysisControl _frequencyAnalysisControl;
        private TimeAnalysisControl _timeAnalysisControl;
        private AdvancedAnalysisControl _advancedAnalysisControl;
        private TrendAnalysisControl _trendAnalysisControl;

        #endregion

        #region 构造函数

        public AlarmAnalysisForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            // 初始化分析服务
            _analysisService = new AlarmAnalysisService();

            // 设置默认时间范围（最近24小时，更适合实际使用）
            var endTime = DateTime.Now;
            var startTime = endTime.AddDays(-1); // 改为1天，更实用

            barEditStartTime.EditValue = startTime;
            barEditEndTime.EditValue = endTime;

            // 设置窗口标题
            this.Text = "报警历史数据分析工具 - v1.0";

            // 初始化DocumentManager
            InitializeDocumentManager();

            // 初始化响应式布局
            InitializeResponsiveLayout();

            // 初始化状态
            SetUIState(false);

            // 清空初始数据，等待用户点击"运行分析"
            ClearAllAnalysisData();
        }

        /// <summary>
        /// 初始化DocumentManager
        /// </summary>
        private void InitializeDocumentManager()
        {
            // 创建分析结果窗体
            CreateAnalysisDocuments();
        }

        /// <summary>
        /// 创建分析结果文档
        /// </summary>
        private void CreateAnalysisDocuments()
        {
            // 创建频率分析控件
            _frequencyAnalysisControl = new FrequencyAnalysisControl
            {
                Dock = DockStyle.Fill
            };

            // 创建时间分析控件
            _timeAnalysisControl = new TimeAnalysisControl
            {
                Dock = DockStyle.Fill
            };

            // 创建高级分析控件
            _advancedAnalysisControl = new AdvancedAnalysisControl
            {
                Dock = DockStyle.Fill
            };

            // 创建趋势分析控件
            _trendAnalysisControl = new TrendAnalysisControl
            {
                Dock = DockStyle.Fill
            };

            // 频率分析标签页
            _frequencyAnalysisForm = new Form
            {
                Text = "频率分析",
                WindowState = FormWindowState.Maximized,
                FormBorderStyle = FormBorderStyle.None
            };
            _frequencyAnalysisForm.Controls.Add(_frequencyAnalysisControl);

            // 时间与行为分析标签页
            _timeAnalysisForm = new Form
            {
                Text = "时间与行为分析",
                WindowState = FormWindowState.Maximized,
                FormBorderStyle = FormBorderStyle.None
            };
            _timeAnalysisForm.Controls.Add(_timeAnalysisControl);

            // 高级分析标签页
            _advancedAnalysisForm = new Form
            {
                Text = "高级分析",
                WindowState = FormWindowState.Maximized,
                FormBorderStyle = FormBorderStyle.None
            };
            _advancedAnalysisForm.Controls.Add(_advancedAnalysisControl);

            // 趋势分析标签页
            _trendAnalysisForm = new Form
            {
                Text = "趋势分析",
                WindowState = FormWindowState.Maximized,
                FormBorderStyle = FormBorderStyle.None
            };
            _trendAnalysisForm.Controls.Add(_trendAnalysisControl);

            // 将窗体添加到DocumentManager
            AddDocumentToManager(_frequencyAnalysisForm);
            AddDocumentToManager(_timeAnalysisForm);
            AddDocumentToManager(_advancedAnalysisForm);
            AddDocumentToManager(_trendAnalysisForm);
        }

        /// <summary>
        /// 添加文档到DocumentManager
        /// </summary>
        /// <param name="form">要添加的窗体</param>
        private void AddDocumentToManager(Form form)
        {
            form.MdiParent = this;
            form.Show();

            // 创建文档
            var document = documentManager.View.AddDocument(form);
            document.Caption = form.Text;
        }

        /// <summary>
        /// 初始化响应式布局
        /// </summary>
        private void InitializeResponsiveLayout()
        {
            // 监听窗口大小变化
            this.Resize += AlarmAnalysisForm_Resize;

            // 设置DPI感知
            this.AutoScaleMode = AutoScaleMode.Font;
        }

        /// <summary>
        /// 窗口大小变化事件处理
        /// </summary>
        private void AlarmAnalysisForm_Resize(object sender, EventArgs e)
        {
            // 确保最小尺寸
            if (this.WindowState == FormWindowState.Normal)
            {
                if (this.Width < this.MinimumSize.Width)
                    this.Width = this.MinimumSize.Width;
                if (this.Height < this.MinimumSize.Height)
                    this.Height = this.MinimumSize.Height;
            }
        }

        #endregion

        #region UI状态管理

        /// <summary>
        /// 设置UI状态
        /// </summary>
        /// <param name="isBusy">是否忙碌状态</param>
        private void SetUIState(bool isBusy)
        {
            _isAnalysisRunning = isBusy;

            // 控制分析相关按钮
            btnRunAnalysis.Enabled = !isBusy;
            barEditStartTime.Enabled = !isBusy;
            barEditEndTime.Enabled = !isBusy;

            // 控制导出按钮（分析完成后才能导出）
            btnExportCsv.Enabled = !isBusy;
            btnExportExcel.Enabled = !isBusy;

            // 更新状态栏
            if (isBusy)
            {
                statusLabel.Caption = "正在执行分析...";
                progressBar.Visibility = BarItemVisibility.Always;
            }
            else
            {
                statusLabel.Caption = "就绪";
                progressBar.Visibility = BarItemVisibility.Never;
            }
        }

        /// <summary>
        /// 获取当前活动的GridControl
        /// </summary>
        /// <returns>活动的GridControl，如果没有则返回null</returns>
        private GridControl GetActiveGridControl()
        {
            try
            {
                // 获取当前活动的文档
                var activeDocument = documentManager.View.ActiveDocument;
                if (activeDocument == null) return null;

                // 获取文档对应的窗体
                var activeForm = activeDocument.Form;
                if (activeForm == null) return null;

                // 根据窗体类型返回对应的GridControl
                if (activeForm == _frequencyAnalysisForm)
                {
                    return _frequencyAnalysisControl?.GridControl;
                }
                else if (activeForm == _timeAnalysisForm)
                {
                    return _timeAnalysisControl?.GridControl;
                }
                else if (activeForm == _advancedAnalysisForm)
                {
                    return _advancedAnalysisControl?.GridControl;
                }
                else if (activeForm == _trendAnalysisForm)
                {
                    return _trendAnalysisControl?.GridControl;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取活动GridControl时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取当前数据类型名称
        /// </summary>
        /// <returns>数据类型名称</returns>
        private string GetCurrentDataTypeName()
        {
            try
            {
                // 获取当前活动的文档
                var activeDocument = documentManager.View.ActiveDocument;
                if (activeDocument?.Form == _frequencyAnalysisForm)
                {
                    return _frequencyAnalysisControl?.CurrentDataTypeName ?? "报警分析数据";
                }

                // TODO: 添加其他分析窗体的数据类型名称获取逻辑

                return "报警分析数据";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取数据类型名称时发生错误: {ex.Message}");
                return "报警分析数据";
            }
        }

        #endregion

        #region 事件处理程序

        /// <summary>
        /// 数据库设置按钮点击事件
        /// </summary>
        private void btnDatabaseSettings_ItemClick(object sender, ItemClickEventArgs e)
        {
            // TODO: 实现数据库配置对话框
            XtraMessageBox.Show("数据库配置功能将在后续任务中实现。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 退出按钮点击事件
        /// </summary>
        private void btnExit_ItemClick(object sender, ItemClickEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 运行分析按钮点击事件
        /// </summary>
        private async void btnRunAnalysis_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                // 验证时间范围
                if (!ValidateTimeRange())
                    return;

                // 设置UI为忙碌状态
                SetUIState(true);

                var startTime = (DateTime)barEditStartTime.EditValue;
                var endTime = (DateTime)barEditEndTime.EditValue;

                statusLabel.Caption = "正在读取数据...";

                // 异步执行分析
                await Task.Run(async () =>
                {
                    try
                    {
                        // 读取数据
                        var alarmEvents = await _analysisService.LoadAlarmDataAsync(startTime, endTime);

                        // 更新状态
                        this.Invoke(new Action(() => statusLabel.Caption = "正在执行基础分析..."));

                        // 执行完整分析
                        var analysisResult = _analysisService.PerformCompleteAnalysis(alarmEvents);

                        // 设置查询时间范围信息
                        analysisResult.QueryTimeRange = $"{startTime:yyyy-MM-dd HH:mm:ss} - {endTime:yyyy-MM-dd HH:mm:ss}";

                        // 在UI线程上更新结果
                        this.Invoke(new Action(() => UpdateAnalysisResults(analysisResult)));
                    }
                    catch (Exception ex)
                    {
                        this.Invoke(new Action(() =>
                        {
                            statusLabel.Caption = "分析失败";
                            XtraMessageBox.Show($"分析过程中发生错误：{ex.Message}", "错误",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }));
                    }
                });

                statusLabel.Caption = "分析完成";
            }
            catch (Exception ex)
            {
                statusLabel.Caption = "分析失败";
                XtraMessageBox.Show($"启动分析时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复UI状态
                SetUIState(false);
            }
        }

        /// <summary>
        /// 验证时间范围
        /// </summary>
        /// <returns>验证是否通过</returns>
        private bool ValidateTimeRange()
        {
            if (barEditStartTime.EditValue == null || barEditEndTime.EditValue == null)
            {
                XtraMessageBox.Show("请选择开始时间和结束时间。", "提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            var startTime = (DateTime)barEditStartTime.EditValue;
            var endTime = (DateTime)barEditEndTime.EditValue;

            if (startTime >= endTime)
            {
                XtraMessageBox.Show("开始时间必须早于结束时间。", "提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            var timeSpan = endTime - startTime;
            if (timeSpan.TotalDays > 365)
            {
                XtraMessageBox.Show("时间范围不能超过365天。", "提示",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 更新分析结果
        /// </summary>
        /// <param name="result">分析结果</param>
        private void UpdateAnalysisResults(CompleteAnalysisResult result)
        {
            if (result == null) return;

            // 更新频率分析控件
            _frequencyAnalysisControl?.UpdateData(
                result.TopAlarmMessages,
                result.TopAlarmingDevices,
                result.TopAlarmingStations
            );

            // 更新时间分析控件
            _timeAnalysisControl?.UpdateData(result.AlarmRates);

            // 更新高级分析控件
            _advancedAnalysisControl?.UpdateData(
                result.LongStandingAlarms,
                result.StaleAlarms
            );

            // 更新趋势分析控件
            _trendAnalysisControl?.UpdateData(result);

            // 显示分析摘要
            var summary = result.GetSummary();
            XtraMessageBox.Show(summary, "分析完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 清空所有分析数据
        /// </summary>
        private void ClearAllAnalysisData()
        {
            // 清空频率分析控件的数据
            _frequencyAnalysisControl?.ClearData();

            // 清空时间分析控件的数据
            _timeAnalysisControl?.ClearData();

            // 清空高级分析控件的数据
            _advancedAnalysisControl?.ClearData();

            // 清空趋势分析控件的数据
            _trendAnalysisControl?.ClearData();

            // 更新状态栏提示
            statusLabel.Caption = "请设置时间范围并点击<运行分析>开始分析";
        }

        /// <summary>
        /// 创建测试数据（用于演示数据绑定）
        /// </summary>
        private void CreateTestData()
        {
            // 创建测试的频率分析数据
            var testAlarmMessages = new List<AlarmFrequencyResult>
            {
                new AlarmFrequencyResult { ItemName = "高温报警", Count = 150 },
                new AlarmFrequencyResult { ItemName = "低压报警", Count = 120 },
                new AlarmFrequencyResult { ItemName = "通信故障", Count = 95 },
                new AlarmFrequencyResult { ItemName = "设备离线", Count = 80 },
                new AlarmFrequencyResult { ItemName = "传感器异常", Count = 65 }
            };

            var testAlarmingDevices = new List<AlarmFrequencyResult>
            {
                new AlarmFrequencyResult { ItemName = "PLC_001", Count = 200 },
                new AlarmFrequencyResult { ItemName = "HMI_002", Count = 180 },
                new AlarmFrequencyResult { ItemName = "SENSOR_003", Count = 160 },
                new AlarmFrequencyResult { ItemName = "MOTOR_004", Count = 140 },
                new AlarmFrequencyResult { ItemName = "VALVE_005", Count = 120 }
            };

            var testAlarmingStations = new List<AlarmFrequencyResult>
            {
                new AlarmFrequencyResult { ItemName = "生产线A", Count = 300 },
                new AlarmFrequencyResult { ItemName = "生产线B", Count = 250 },
                new AlarmFrequencyResult { ItemName = "包装车间", Count = 200 },
                new AlarmFrequencyResult { ItemName = "仓储区", Count = 150 },
                new AlarmFrequencyResult { ItemName = "办公区", Count = 100 }
            };

            // 更新频率分析控件
            _frequencyAnalysisControl?.UpdateData(
                testAlarmMessages,
                testAlarmingDevices,
                testAlarmingStations
            );
        }

        /// <summary>
        /// 导出CSV按钮点击事件
        /// </summary>
        private void btnExportCsv_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                var activeGrid = GetActiveGridControl();
                if (activeGrid == null)
                {
                    XtraMessageBox.Show("没有可导出的数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using (var sfd = new SaveFileDialog())
                {
                    sfd.Filter = "CSV 文件 (*.csv)|*.csv";
                    sfd.Title = "导出CSV文件";

                    var dataTypeName = GetCurrentDataTypeName();
                    sfd.FileName = $"{dataTypeName}_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        // 配置CSV导出选项
                        var options = new DevExpress.XtraPrinting.CsvExportOptions();
                        options.Encoding = System.Text.Encoding.UTF8;
                        options.Separator = ",";
                        options.TextExportMode = DevExpress.XtraPrinting.TextExportMode.Text;

                        activeGrid.ExportToCsv(sfd.FileName, options);
                        XtraMessageBox.Show($"数据已成功导出到：{sfd.FileName}", "导出完成",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"导出CSV时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导出Excel按钮点击事件
        /// </summary>
        private void btnExportExcel_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                var activeGrid = GetActiveGridControl();
                if (activeGrid == null)
                {
                    XtraMessageBox.Show("没有可导出的数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                using (var sfd = new SaveFileDialog())
                {
                    sfd.Filter = "Excel 文件 (*.xlsx)|*.xlsx";
                    sfd.Title = "导出Excel文件";

                    var dataTypeName = GetCurrentDataTypeName();
                    sfd.FileName = $"{dataTypeName}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        // 配置Excel导出选项
                        var options = new DevExpress.XtraPrinting.XlsxExportOptions();
                        options.TextExportMode = DevExpress.XtraPrinting.TextExportMode.Text;
                        options.ShowGridLines = true;
                        options.ExportHyperlinks = false;

                        activeGrid.ExportToXlsx(sfd.FileName, options);
                        XtraMessageBox.Show($"数据已成功导出到：{sfd.FileName}", "导出完成",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"导出Excel时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 资源清理

        /// <summary>
        /// 清理自定义资源
        /// </summary>
        private void CleanupCustomResources()
        {
            // 清理分析服务
            _analysisService?.Dispose();

            // 清理分析控件
            _frequencyAnalysisControl?.Dispose();

            // 清理分析结果窗体
            _frequencyAnalysisForm?.Dispose();
            _timeAnalysisForm?.Dispose();
            _advancedAnalysisForm?.Dispose();
            _trendAnalysisForm?.Dispose();
        }

        #endregion
    }
}

// {{END_MODIFICATIONS}}