﻿namespace AlarmAnalysis
{
    partial class AlarmAnalysisForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                {
                    components.Dispose();
                }

                // 清理自定义资源
                try
                {
                    CleanupCustomResources();
                }
                catch
                {
                    // 忽略清理过程中的异常
                }
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.ribbon = new DevExpress.XtraBars.Ribbon.RibbonControl();

            // Ribbon Items
            this.btnDatabaseSettings = new DevExpress.XtraBars.BarButtonItem();
            this.btnExit = new DevExpress.XtraBars.BarButtonItem();
            this.repositoryItemDateEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.repositoryItemDateEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.barEditStartTime = new DevExpress.XtraBars.BarEditItem();
            this.barEditEndTime = new DevExpress.XtraBars.BarEditItem();
            this.btnRunAnalysis = new DevExpress.XtraBars.BarButtonItem();
            this.btnExportCsv = new DevExpress.XtraBars.BarButtonItem();
            this.btnExportExcel = new DevExpress.XtraBars.BarButtonItem();

            // Ribbon Pages
            this.ribbonPageFile = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageHome = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageView = new DevExpress.XtraBars.Ribbon.RibbonPage();

            // Ribbon Page Groups
            this.ribbonPageGroupDatabase = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupAnalysisPeriod = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupExecuteAnalysis = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupExport = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroupDisplay = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();

            this.ribbonStatusBar = new DevExpress.XtraBars.Ribbon.RibbonStatusBar();
            this.statusLabel = new DevExpress.XtraBars.BarStaticItem();
            this.progressBar = new DevExpress.XtraBars.BarEditItem();
            this.repositoryItemMarqueeProgressBar1 = new DevExpress.XtraEditors.Repository.RepositoryItemMarqueeProgressBar();

            // DocumentManager
            this.documentManager = new DevExpress.XtraBars.Docking2010.DocumentManager(this.components);
            this.tabbedView = new DevExpress.XtraBars.Docking2010.Views.Tabbed.TabbedView(this.components);

            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemDateEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemDateEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMarqueeProgressBar1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.documentManager)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedView)).BeginInit();
            this.SuspendLayout();
            //
            // ribbon
            //
            this.ribbon.ExpandCollapseItem.Id = 0;
            this.ribbon.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbon.ExpandCollapseItem,
            this.btnDatabaseSettings,
            this.btnExit,
            this.barEditStartTime,
            this.barEditEndTime,
            this.btnRunAnalysis,
            this.btnExportCsv,
            this.btnExportExcel,
            this.statusLabel,
            this.progressBar});
            this.ribbon.Location = new System.Drawing.Point(0, 0);
            this.ribbon.MaxItemId = 10;
            this.ribbon.Name = "ribbon";
            this.ribbon.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.ribbonPageFile,
            this.ribbonPageHome,
            this.ribbonPageView});
            this.ribbon.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemDateEdit1,
            this.repositoryItemDateEdit2,
            this.repositoryItemMarqueeProgressBar1});
            this.ribbon.Size = new System.Drawing.Size(1997, 271);
            this.ribbon.StatusBar = this.ribbonStatusBar;
            //
            // btnDatabaseSettings
            //
            this.btnDatabaseSettings.Caption = "数据库设置";
            this.btnDatabaseSettings.Id = 1;
            this.btnDatabaseSettings.Name = "btnDatabaseSettings";
            this.btnDatabaseSettings.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnDatabaseSettings_ItemClick);
            //
            // btnExit
            //
            this.btnExit.Caption = "退出";
            this.btnExit.Id = 2;
            this.btnExit.Name = "btnExit";
            this.btnExit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnExit_ItemClick);
            //
            // repositoryItemDateEdit1
            //
            this.repositoryItemDateEdit1.AutoHeight = false;
            this.repositoryItemDateEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemDateEdit1.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemDateEdit1.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.repositoryItemDateEdit1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.repositoryItemDateEdit1.EditFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.repositoryItemDateEdit1.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.repositoryItemDateEdit1.Mask.EditMask = "yyyy-MM-dd HH:mm:ss";
            this.repositoryItemDateEdit1.Name = "repositoryItemDateEdit1";
            //
            // repositoryItemDateEdit2
            //
            this.repositoryItemDateEdit2.AutoHeight = false;
            this.repositoryItemDateEdit2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemDateEdit2.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repositoryItemDateEdit2.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.repositoryItemDateEdit2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.repositoryItemDateEdit2.EditFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            this.repositoryItemDateEdit2.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.repositoryItemDateEdit2.Mask.EditMask = "yyyy-MM-dd HH:mm:ss";
            this.repositoryItemDateEdit2.Name = "repositoryItemDateEdit2";
            //
            // barEditStartTime
            //
            this.barEditStartTime.Caption = "开始时间";
            this.barEditStartTime.Edit = this.repositoryItemDateEdit1;
            this.barEditStartTime.Id = 3;
            this.barEditStartTime.Name = "barEditStartTime";
            this.barEditStartTime.Width = 150;
            //
            // barEditEndTime
            //
            this.barEditEndTime.Caption = "结束时间";
            this.barEditEndTime.Edit = this.repositoryItemDateEdit2;
            this.barEditEndTime.Id = 4;
            this.barEditEndTime.Name = "barEditEndTime";
            this.barEditEndTime.Width = 150;
            //
            // btnRunAnalysis
            //
            this.btnRunAnalysis.Caption = "运行分析";
            this.btnRunAnalysis.Id = 5;
            this.btnRunAnalysis.Name = "btnRunAnalysis";
            this.btnRunAnalysis.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.btnRunAnalysis.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnRunAnalysis_ItemClick);
            //
            // btnExportCsv
            //
            this.btnExportCsv.Caption = "导出CSV";
            this.btnExportCsv.Id = 6;
            this.btnExportCsv.Name = "btnExportCsv";
            this.btnExportCsv.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnExportCsv_ItemClick);
            //
            // btnExportExcel
            //
            this.btnExportExcel.Caption = "导出Excel";
            this.btnExportExcel.Id = 7;
            this.btnExportExcel.Name = "btnExportExcel";
            this.btnExportExcel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnExportExcel_ItemClick);
            //
            // ribbonStatusBar
            //
            this.ribbonStatusBar.ItemLinks.Add(this.statusLabel);
            this.ribbonStatusBar.ItemLinks.Add(this.progressBar);
            this.ribbonStatusBar.Location = new System.Drawing.Point(0, 1011);
            this.ribbonStatusBar.Name = "ribbonStatusBar";
            this.ribbonStatusBar.Ribbon = this.ribbon;
            this.ribbonStatusBar.Size = new System.Drawing.Size(1997, 42);
            //
            // statusLabel
            //
            this.statusLabel.Caption = "就绪";
            this.statusLabel.Id = 8;
            this.statusLabel.Name = "statusLabel";
            //
            // progressBar
            //
            this.progressBar.Caption = "进度";
            this.progressBar.Edit = this.repositoryItemMarqueeProgressBar1;
            this.progressBar.Id = 9;
            this.progressBar.Name = "progressBar";
            this.progressBar.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.progressBar.Width = 200;
            //
            // repositoryItemMarqueeProgressBar1
            //
            this.repositoryItemMarqueeProgressBar1.Name = "repositoryItemMarqueeProgressBar1";
            //
            // ribbonPageFile
            //
            this.ribbonPageFile.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupDatabase});
            this.ribbonPageFile.Name = "ribbonPageFile";
            this.ribbonPageFile.Text = "文件";
            //
            // ribbonPageHome
            //
            this.ribbonPageHome.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupAnalysisPeriod,
            this.ribbonPageGroupExecuteAnalysis,
            this.ribbonPageGroupExport});
            this.ribbonPageHome.Name = "ribbonPageHome";
            this.ribbonPageHome.Text = "主页";
            //
            // ribbonPageView
            //
            this.ribbonPageView.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroupDisplay});
            this.ribbonPageView.Name = "ribbonPageView";
            this.ribbonPageView.Text = "视图";
            //
            // documentManager
            //
            this.documentManager.MdiParent = this;
            this.documentManager.MenuManager = this.ribbon;
            this.documentManager.View = this.tabbedView;
            this.documentManager.ViewCollection.AddRange(new DevExpress.XtraBars.Docking2010.Views.BaseView[] {
            this.tabbedView});
            //
            // tabbedView
            //
            this.tabbedView.DocumentGroupProperties.HeaderLocation = DevExpress.XtraTab.TabHeaderLocation.Top;
            this.tabbedView.DocumentGroupProperties.ShowTabHeader = true;
            this.tabbedView.DocumentProperties.AllowClose = false;
            this.tabbedView.DocumentProperties.AllowFloat = false;
            this.tabbedView.DocumentProperties.AllowFloatOnDoubleClick = false;
            //
            // AlarmAnalysisForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 23F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1997, 1053);
            this.Controls.Add(this.ribbonStatusBar);
            this.Controls.Add(this.ribbon);
            this.IsMdiContainer = true;
            this.MinimumSize = new System.Drawing.Size(1024, 768);
            this.Name = "AlarmAnalysisForm";
            this.Ribbon = this.ribbon;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.StatusBar = this.ribbonStatusBar;
            this.Text = "报警历史数据分析工具";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemDateEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemDateEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMarqueeProgressBar1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.documentManager)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedView)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

            //
            // ribbonPageGroupDatabase
            //
            this.ribbonPageGroupDatabase.ItemLinks.Add(this.btnDatabaseSettings);
            this.ribbonPageGroupDatabase.ItemLinks.Add(this.btnExit);
            this.ribbonPageGroupDatabase.Name = "ribbonPageGroupDatabase";
            this.ribbonPageGroupDatabase.Text = "数据库";
            //
            // ribbonPageGroupAnalysisPeriod
            //
            this.ribbonPageGroupAnalysisPeriod.ItemLinks.Add(this.barEditStartTime);
            this.ribbonPageGroupAnalysisPeriod.ItemLinks.Add(this.barEditEndTime);
            this.ribbonPageGroupAnalysisPeriod.Name = "ribbonPageGroupAnalysisPeriod";
            this.ribbonPageGroupAnalysisPeriod.Text = "分析周期";
            //
            // ribbonPageGroupExecuteAnalysis
            //
            this.ribbonPageGroupExecuteAnalysis.ItemLinks.Add(this.btnRunAnalysis);
            this.ribbonPageGroupExecuteAnalysis.Name = "ribbonPageGroupExecuteAnalysis";
            this.ribbonPageGroupExecuteAnalysis.Text = "执行分析";
            //
            // ribbonPageGroupExport
            //
            this.ribbonPageGroupExport.ItemLinks.Add(this.btnExportCsv);
            this.ribbonPageGroupExport.ItemLinks.Add(this.btnExportExcel);
            this.ribbonPageGroupExport.Name = "ribbonPageGroupExport";
            this.ribbonPageGroupExport.Text = "导出";
            //
            // ribbonPageGroupDisplay
            //
            this.ribbonPageGroupDisplay.Name = "ribbonPageGroupDisplay";
            this.ribbonPageGroupDisplay.Text = "显示选项";

        }

        #endregion

        private DevExpress.XtraBars.Ribbon.RibbonControl ribbon;
        private DevExpress.XtraBars.Ribbon.RibbonStatusBar ribbonStatusBar;

        // Ribbon Items
        private DevExpress.XtraBars.BarButtonItem btnDatabaseSettings;
        private DevExpress.XtraBars.BarButtonItem btnExit;
        private DevExpress.XtraBars.BarEditItem barEditStartTime;
        private DevExpress.XtraBars.BarEditItem barEditEndTime;
        private DevExpress.XtraBars.BarButtonItem btnRunAnalysis;
        private DevExpress.XtraBars.BarButtonItem btnExportCsv;
        private DevExpress.XtraBars.BarButtonItem btnExportExcel;
        private DevExpress.XtraBars.BarStaticItem statusLabel;
        private DevExpress.XtraBars.BarEditItem progressBar;

        // Repository Items
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit repositoryItemDateEdit1;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit repositoryItemDateEdit2;
        private DevExpress.XtraEditors.Repository.RepositoryItemMarqueeProgressBar repositoryItemMarqueeProgressBar1;

        // Ribbon Pages
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPageFile;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPageHome;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPageView;

        // Ribbon Page Groups
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupDatabase;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupAnalysisPeriod;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupExecuteAnalysis;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupExport;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroupDisplay;

        // DocumentManager
        private DevExpress.XtraBars.Docking2010.DocumentManager documentManager;
        private DevExpress.XtraBars.Docking2010.Views.Tabbed.TabbedView tabbedView;
    }
}