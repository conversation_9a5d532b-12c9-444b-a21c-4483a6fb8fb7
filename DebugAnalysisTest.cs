// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#004"
//   Timestamp: "2025-08-24T18:30:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "调试测试程序，验证分析数据生成是否正确"
// }}
// {{START_MODIFICATIONS}}

using System;
using System.Collections.Generic;
using System.Linq;
using AlarmAnalysis.Models;
using AlarmAnalysis.Services;
using AlarmAnalysis.Analysis;

namespace AlarmAnalysis
{
    /// <summary>
    /// 调试分析测试程序
    /// 用于验证分析数据是否正确生成
    /// </summary>
    public static class DebugAnalysisTest
    {
        /// <summary>
        /// 执行调试测试
        /// </summary>
        public static void RunDebugTest()
        {
            Console.WriteLine("=== 调试分析测试 ===");
            
            try
            {
                // 创建测试数据
                var testEvents = CreateTestAlarmEvents();
                Console.WriteLine($"创建测试数据: {testEvents.Count} 个报警事件");
                
                // 创建分析服务
                var analysisService = new AlarmAnalysisService();
                
                // 执行完整分析
                var result = analysisService.PerformCompleteAnalysis(testEvents);
                
                // 验证结果
                VerifyAnalysisResult(result);
                
                Console.WriteLine("✅ 调试测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 调试测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }
        
        /// <summary>
        /// 创建测试报警事件
        /// </summary>
        /// <returns>测试报警事件列表</returns>
        private static List<AlarmEvent> CreateTestAlarmEvents()
        {
            var events = new List<AlarmEvent>();
            var baseTime = DateTime.Now.AddHours(-24);
            
            // 创建不同类型的报警事件
            for (int i = 0; i < 100; i++)
            {
                events.Add(new AlarmEvent
                {
                    EventId = i + 1,
                    EventDateTime = baseTime.AddMinutes(i * 10),
                    SourceName = $"Device_{i % 10:D2}",
                    Station = $"Station_{i % 5:D2}",
                    Device = $"Device_{i % 10:D2}",
                    EventMessage = GetTestEventMessage(i % 5),
                    EventState = GetTestEventState(i % 4),
                    Priority = (i % 3) + 1,
                    Category = GetTestCategory(i % 3),
                    Severity = GetTestSeverity(i % 4)
                });
            }
            
            return events;
        }
        
        /// <summary>
        /// 获取测试事件消息
        /// </summary>
        private static string GetTestEventMessage(int index)
        {
            var messages = new[]
            {
                "高温报警",
                "通信故障",
                "压力异常",
                "液位报警",
                "流量异常"
            };
            return messages[index];
        }
        
        /// <summary>
        /// 获取测试事件状态
        /// </summary>
        private static string GetTestEventState(int index)
        {
            var states = new[] { "Active", "Acknowledged", "Resolved", "Cleared" };
            return states[index];
        }
        
        /// <summary>
        /// 获取测试类别
        /// </summary>
        private static string GetTestCategory(int index)
        {
            var categories = new[] { "Process", "Equipment", "Safety" };
            return categories[index];
        }
        
        /// <summary>
        /// 获取测试严重性
        /// </summary>
        private static string GetTestSeverity(int index)
        {
            var severities = new[] { "Low", "Medium", "High", "Critical" };
            return severities[index];
        }
        
        /// <summary>
        /// 验证分析结果
        /// </summary>
        /// <param name="result">分析结果</param>
        private static void VerifyAnalysisResult(CompleteAnalysisResult result)
        {
            Console.WriteLine("\n--- 分析结果验证 ---");
            
            // 基础信息
            Console.WriteLine($"总事件数: {result.TotalEvents}");
            Console.WriteLine($"分析时间: {result.AnalysisTime}");
            Console.WriteLine($"数据时间范围: {result.DataTimeRange}");
            
            // 频率分析
            Console.WriteLine($"\nTop报警消息: {result.TopAlarmMessages?.Count ?? 0} 项");
            if (result.TopAlarmMessages?.Any() == true)
            {
                foreach (var msg in result.TopAlarmMessages.Take(3))
                {
                    Console.WriteLine($"  {msg.ItemName}: {msg.Count} 次 ({msg.Percentage:F1}%)");
                }
            }
            
            Console.WriteLine($"Top报警设备: {result.TopAlarmingDevices?.Count ?? 0} 项");
            Console.WriteLine($"Top报警站点: {result.TopAlarmingStations?.Count ?? 0} 项");
            
            // 报警率分析
            if (result.AlarmRates != null)
            {
                Console.WriteLine($"\n报警率分析:");
                Console.WriteLine($"  总报警数: {result.AlarmRates.TotalAlarms}");
                Console.WriteLine($"  每小时报警率: {result.AlarmRates.AlarmsPerHour:F2}");
                Console.WriteLine($"  每天报警率: {result.AlarmRates.AlarmsPerDay:F2}");
                Console.WriteLine($"  时间跨度: {result.AlarmRates.TimeSpan}");
                
                if (result.AlarmRates.PeakAlarmRate != null)
                {
                    Console.WriteLine($"  峰值报警率: {result.AlarmRates.PeakAlarmRate.MaxAlarmsInWindow} 次/{result.AlarmRates.PeakAlarmRate.WindowSize.TotalMinutes:F0}分钟");
                }
            }
            else
            {
                Console.WriteLine("\n❌ 报警率分析数据为空!");
            }
            
            // 高级分析
            Console.WriteLine($"\n长期持续报警: {result.LongStandingAlarms?.Count ?? 0} 项");
            if (result.LongStandingAlarms?.Any() == true)
            {
                foreach (var alarm in result.LongStandingAlarms.Take(3))
                {
                    Console.WriteLine($"  {alarm.SourceName}: {alarm.EventMessage} (持续 {alarm.Duration})");
                }
            }
            
            Console.WriteLine($"陈旧报警: {result.StaleAlarms?.Count ?? 0} 项");
            if (result.StaleAlarms?.Any() == true)
            {
                foreach (var alarm in result.StaleAlarms.Take(3))
                {
                    Console.WriteLine($"  {alarm.SourceName}: {alarm.EventMessage} (陈旧 {alarm.StaleDuration})");
                }
            }
            
            // 验证关键数据
            var hasFrequencyData = result.TopAlarmMessages?.Any() == true;
            var hasRateData = result.AlarmRates != null;
            var hasAdvancedData = (result.LongStandingAlarms?.Any() == true) || (result.StaleAlarms?.Any() == true);
            
            Console.WriteLine($"\n--- 数据完整性检查 ---");
            Console.WriteLine($"频率分析数据: {(hasFrequencyData ? "✅" : "❌")}");
            Console.WriteLine($"报警率数据: {(hasRateData ? "✅" : "❌")}");
            Console.WriteLine($"高级分析数据: {(hasAdvancedData ? "✅" : "❌")}");
            
            if (!hasRateData)
            {
                Console.WriteLine("⚠️  报警率数据缺失，这可能是其他tab页面无数据的原因!");
            }
        }
    }
}

// {{END_MODIFICATIONS}}
