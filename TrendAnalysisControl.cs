// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#003"
//   Timestamp: "2025-08-24T18:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "新增趋势分析控件，显示基础统计信息和扩展分析结果"
// }}
// {{START_MODIFICATIONS}}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Services;

namespace AlarmAnalysis
{
    /// <summary>
    /// 趋势分析控件
    /// 显示趋势分析、统计摘要等扩展分析结果
    /// </summary>
    public partial class TrendAnalysisControl : DevExpress.XtraEditors.XtraUserControl
    {
        #region 私有字段

        private CompleteAnalysisResult _analysisResult;

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取GridControl实例（用于导出功能）
        /// </summary>
        public DevExpress.XtraGrid.GridControl GridControl => gridControl;

        /// <summary>
        /// 获取当前显示的数据类型名称
        /// </summary>
        public string CurrentDataTypeName => "趋势分析";

        #endregion

        #region 构造函数

        public TrendAnalysisControl()
        {
            InitializeComponent();
            InitializeControl();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControl()
        {
            try
            {
                // 配置GridView
                ConfigureGridView();
                
                // 初始化数据
                ClearData();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"初始化趋势分析控件时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 配置GridView
        /// </summary>
        private void ConfigureGridView()
        {
            // 基本设置
            gridView.OptionsView.ShowGroupPanel = false;
            gridView.OptionsView.ColumnAutoWidth = false;
            gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView.OptionsSelection.EnableAppearanceFocusedRow = true;
            
            // 禁用编辑
            gridView.OptionsBehavior.Editable = false;
            gridView.OptionsBehavior.ReadOnly = true;
            
            // 配置列
            ConfigureGridColumns();
        }

        /// <summary>
        /// 配置表格列
        /// </summary>
        private void ConfigureGridColumns()
        {
            // 清空现有列
            gridView.Columns.Clear();
            
            // 添加列
            var colCategory = gridView.Columns.AddField("Category");
            colCategory.Caption = "分析类别";
            colCategory.Width = 150;
            colCategory.OptionsColumn.AllowEdit = false;
            
            var colMetric = gridView.Columns.AddField("Metric");
            colMetric.Caption = "指标名称";
            colMetric.Width = 200;
            colMetric.OptionsColumn.AllowEdit = false;
            
            var colValue = gridView.Columns.AddField("Value");
            colValue.Caption = "数值";
            colValue.Width = 120;
            colValue.OptionsColumn.AllowEdit = false;
            
            var colDescription = gridView.Columns.AddField("Description");
            colDescription.Caption = "说明";
            colDescription.Width = 300;
            colDescription.OptionsColumn.AllowEdit = false;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新分析数据
        /// </summary>
        /// <param name="analysisResult">完整分析结果</param>
        public void UpdateData(CompleteAnalysisResult analysisResult)
        {
            try
            {
                _analysisResult = analysisResult;
                
                // 更新显示
                UpdateDisplay();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"更新趋势分析数据时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空数据
        /// </summary>
        public void ClearData()
        {
            try
            {
                _analysisResult = null;
                gridControl.DataSource = null;
                gridView.RefreshData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清空趋势分析数据时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新显示
        /// </summary>
        private void UpdateDisplay()
        {
            if (_analysisResult == null)
            {
                ClearData();
                return;
            }

            // 创建显示数据
            var displayData = CreateDisplayData(_analysisResult);
            
            // 绑定数据
            gridControl.DataSource = displayData;
            
            // 自动调整列宽
            gridView.BestFitColumns();
        }

        /// <summary>
        /// 创建显示数据
        /// </summary>
        /// <param name="result">分析结果</param>
        /// <returns>显示数据列表</returns>
        private List<object> CreateDisplayData(CompleteAnalysisResult result)
        {
            var displayData = new List<object>();

            // 基础统计
            displayData.Add(new
            {
                Category = "基础统计",
                Metric = "分析时间",
                Value = result.AnalysisTime.ToString("yyyy-MM-dd HH:mm:ss"),
                Description = "执行分析的时间"
            });

            displayData.Add(new
            {
                Category = "基础统计",
                Metric = "总事件数",
                Value = result.TotalEvents.ToString("N0"),
                Description = "分析时间范围内的报警事件总数"
            });

            displayData.Add(new
            {
                Category = "基础统计",
                Metric = "数据时间范围",
                Value = result.DataTimeRange ?? "未知",
                Description = "实际数据的时间跨度"
            });

            if (!string.IsNullOrEmpty(result.QueryTimeRange))
            {
                displayData.Add(new
                {
                    Category = "基础统计",
                    Metric = "查询时间范围",
                    Value = result.QueryTimeRange,
                    Description = "用户设置的查询时间范围"
                });
            }

            // 频率分析统计
            if (result.TopAlarmMessages?.Any() == true)
            {
                displayData.Add(new
                {
                    Category = "频率分析",
                    Metric = "Top报警消息数",
                    Value = result.TopAlarmMessages.Count.ToString(),
                    Description = "频率最高的报警消息类型数量"
                });

                var topMessage = result.TopAlarmMessages.First();
                displayData.Add(new
                {
                    Category = "频率分析",
                    Metric = "最高频报警",
                    Value = $"{topMessage.Count}次 ({topMessage.Percentage:F1}%)",
                    Description = $"最频繁的报警: {topMessage.ItemName}"
                });
            }

            if (result.TopAlarmingDevices?.Any() == true)
            {
                displayData.Add(new
                {
                    Category = "频率分析",
                    Metric = "Top报警设备数",
                    Value = result.TopAlarmingDevices.Count.ToString(),
                    Description = "报警频率最高的设备数量"
                });
            }

            if (result.TopAlarmingStations?.Any() == true)
            {
                displayData.Add(new
                {
                    Category = "频率分析",
                    Metric = "Top报警站点数",
                    Value = result.TopAlarmingStations.Count.ToString(),
                    Description = "报警频率最高的站点数量"
                });
            }

            // 报警率统计
            if (result.AlarmRates != null)
            {
                displayData.Add(new
                {
                    Category = "时间分析",
                    Metric = "平均报警率",
                    Value = $"{result.AlarmRates.AlarmsPerHour:F2} 次/小时",
                    Description = "每小时平均报警次数"
                });

                if (result.AlarmRates.PeakAlarmRate != null)
                {
                    displayData.Add(new
                    {
                        Category = "时间分析",
                        Metric = "峰值报警率",
                        Value = $"{result.AlarmRates.PeakAlarmRate.Count} 次/{result.AlarmRates.PeakAlarmRate.TimeWindow}分钟",
                        Description = $"峰值时间: {result.AlarmRates.PeakAlarmRate.StartTime:HH:mm:ss}"
                    });
                }
            }

            // 高级分析统计
            if (result.LongStandingAlarms?.Any() == true)
            {
                displayData.Add(new
                {
                    Category = "高级分析",
                    Metric = "长期持续报警数",
                    Value = result.LongStandingAlarms.Count.ToString(),
                    Description = "持续时间较长的报警数量"
                });
            }

            if (result.StaleAlarms?.Any() == true)
            {
                displayData.Add(new
                {
                    Category = "高级分析",
                    Metric = "陈旧报警数",
                    Value = result.StaleAlarms.Count.ToString(),
                    Description = "长时间未更新的报警数量"
                });
            }

            return displayData;
        }

        #endregion
    }
}

// {{END_MODIFICATIONS}}
