// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#002"
//   Timestamp: "2025-08-24T18:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "新增高级分析控件，使用TabControl分别显示长期持续报警和陈旧报警"
// }}
// {{START_MODIFICATIONS}}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraTab;
using AlarmAnalysis.Analysis;

namespace AlarmAnalysis
{
    /// <summary>
    /// 高级分析控件
    /// 显示长期持续报警和陈旧报警等高级分析结果
    /// </summary>
    public partial class AdvancedAnalysisControl : DevExpress.XtraEditors.XtraUserControl
    {
        #region 私有字段

        private List<LongStandingAlarmResult> _longStandingAlarms;
        private List<StaleAlarmResult> _staleAlarms;

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取当前活动的GridControl实例（用于导出功能）
        /// </summary>
        public DevExpress.XtraGrid.GridControl GridControl
        {
            get
            {
                if (tabControl.SelectedTabPage == tabPageLongStanding)
                    return gridControlLongStanding;
                else if (tabControl.SelectedTabPage == tabPageStale)
                    return gridControlStale;
                return null;
            }
        }

        /// <summary>
        /// 获取当前显示的数据类型名称
        /// </summary>
        public string CurrentDataTypeName
        {
            get
            {
                if (tabControl.SelectedTabPage == tabPageLongStanding)
                    return "长期持续报警";
                else if (tabControl.SelectedTabPage == tabPageStale)
                    return "陈旧报警";
                return "高级分析";
            }
        }

        #endregion

        #region 构造函数

        public AdvancedAnalysisControl()
        {
            InitializeComponent();
            InitializeControl();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControl()
        {
            try
            {
                // 配置GridView
                ConfigureLongStandingGridView();
                ConfigureStaleGridView();
                
                // 初始化数据
                ClearData();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"初始化高级分析控件时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 配置长期持续报警GridView
        /// </summary>
        private void ConfigureLongStandingGridView()
        {
            // 基本设置
            gridViewLongStanding.OptionsView.ShowGroupPanel = false;
            gridViewLongStanding.OptionsView.ColumnAutoWidth = false;
            gridViewLongStanding.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridViewLongStanding.OptionsSelection.EnableAppearanceFocusedRow = true;
            
            // 禁用编辑
            gridViewLongStanding.OptionsBehavior.Editable = false;
            gridViewLongStanding.OptionsBehavior.ReadOnly = true;
            
            // 配置列
            ConfigureLongStandingColumns();
        }

        /// <summary>
        /// 配置陈旧报警GridView
        /// </summary>
        private void ConfigureStaleGridView()
        {
            // 基本设置
            gridViewStale.OptionsView.ShowGroupPanel = false;
            gridViewStale.OptionsView.ColumnAutoWidth = false;
            gridViewStale.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridViewStale.OptionsSelection.EnableAppearanceFocusedRow = true;
            
            // 禁用编辑
            gridViewStale.OptionsBehavior.Editable = false;
            gridViewStale.OptionsBehavior.ReadOnly = true;
            
            // 配置列
            ConfigureStaleColumns();
        }

        /// <summary>
        /// 配置长期持续报警表格列
        /// </summary>
        private void ConfigureLongStandingColumns()
        {
            // 让GridControl自动生成列
            gridViewLongStanding.OptionsBehavior.AutoPopulateColumns = true;
        }

        /// <summary>
        /// 配置陈旧报警表格列
        /// </summary>
        private void ConfigureStaleColumns()
        {
            // 让GridControl自动生成列
            gridViewStale.OptionsBehavior.AutoPopulateColumns = true;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新分析数据
        /// </summary>
        /// <param name="longStandingAlarms">长期持续报警数据</param>
        /// <param name="staleAlarms">陈旧报警数据</param>
        public void UpdateData(List<LongStandingAlarmResult> longStandingAlarms,
                              List<StaleAlarmResult> staleAlarms)
        {
            try
            {
                // 调试信息
                System.Diagnostics.Debug.WriteLine($"AdvancedAnalysisControl.UpdateData called");
                System.Diagnostics.Debug.WriteLine($"  LongStandingAlarms: {longStandingAlarms?.Count ?? 0} items");
                System.Diagnostics.Debug.WriteLine($"  StaleAlarms: {staleAlarms?.Count ?? 0} items");

                _longStandingAlarms = longStandingAlarms ?? new List<LongStandingAlarmResult>();
                _staleAlarms = staleAlarms ?? new List<StaleAlarmResult>();

                // 更新显示
                UpdateLongStandingDisplay();
                UpdateStaleDisplay();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"更新高级分析数据时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空数据
        /// </summary>
        public void ClearData()
        {
            try
            {
                _longStandingAlarms = null;
                _staleAlarms = null;

                // 显示占位符数据
                var placeholderLongStanding = new List<object>
                {
                    new { SourceName = "等待数据", Station = "", Device = "", EventMessage = "请运行分析以查看长期持续报警",
                          CurrentState = "", FirstOccurrence = DateTime.Now, DurationText = "", OccurrenceCount = 0, Priority = 0 }
                };

                var placeholderStale = new List<object>
                {
                    new { SourceName = "等待数据", Station = "", Device = "", EventMessage = "请运行分析以查看陈旧报警",
                          CurrentState = "", LastUpdate = DateTime.Now, StaleDurationText = "", StateTransitions = 0 }
                };

                gridControlLongStanding.DataSource = placeholderLongStanding;
                gridControlStale.DataSource = placeholderStale;

                gridViewLongStanding.RefreshData();
                gridViewStale.RefreshData();

                System.Diagnostics.Debug.WriteLine("AdvancedAnalysisControl: Data cleared, showing placeholders");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清空高级分析数据时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新长期持续报警显示
        /// </summary>
        private void UpdateLongStandingDisplay()
        {
            if (_longStandingAlarms == null || !_longStandingAlarms.Any())
            {
                gridControlLongStanding.DataSource = null;
                return;
            }

            // 创建显示数据
            var displayData = _longStandingAlarms.Select(alarm => new
            {
                alarm.SourceName,
                alarm.Station,
                alarm.Device,
                alarm.EventMessage,
                alarm.CurrentState,
                alarm.FirstOccurrence,
                DurationText = FormatTimeSpan(alarm.Duration),
                alarm.OccurrenceCount,
                alarm.Priority
            }).ToList();

            gridControlLongStanding.DataSource = displayData;
            gridViewLongStanding.BestFitColumns();
            gridViewLongStanding.RefreshData();
            gridControlLongStanding.Refresh();

            System.Diagnostics.Debug.WriteLine($"AdvancedAnalysisControl: LongStanding data bound with {displayData.Count} items");
        }

        /// <summary>
        /// 更新陈旧报警显示
        /// </summary>
        private void UpdateStaleDisplay()
        {
            if (_staleAlarms == null || !_staleAlarms.Any())
            {
                gridControlStale.DataSource = null;
                return;
            }

            // 创建显示数据
            var displayData = _staleAlarms.Select(alarm => new
            {
                alarm.SourceName,
                alarm.Station,
                alarm.Device,
                alarm.EventMessage,
                alarm.CurrentState,
                alarm.LastUpdate,
                StaleDurationText = FormatTimeSpan(alarm.StaleDuration),
                alarm.StateTransitions
            }).ToList();

            gridControlStale.DataSource = displayData;
            gridViewStale.BestFitColumns();
            gridViewStale.RefreshData();
            gridControlStale.Refresh();

            System.Diagnostics.Debug.WriteLine($"AdvancedAnalysisControl: Stale data bound with {displayData.Count} items");
        }

        /// <summary>
        /// 格式化时间跨度
        /// </summary>
        /// <param name="timeSpan">时间跨度</param>
        /// <returns>格式化字符串</returns>
        private string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
                return $"{timeSpan.TotalDays:F1}天";
            else if (timeSpan.TotalHours >= 1)
                return $"{timeSpan.TotalHours:F1}小时";
            else if (timeSpan.TotalMinutes >= 1)
                return $"{timeSpan.TotalMinutes:F1}分钟";
            else
                return $"{timeSpan.TotalSeconds:F1}秒";
        }

        #endregion
    }
}

// {{END_MODIFICATIONS}}
