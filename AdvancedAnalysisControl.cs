// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#002"
//   Timestamp: "2025-08-24T18:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "新增高级分析控件，使用TabControl分别显示长期持续报警和陈旧报警"
// }}
// {{START_MODIFICATIONS}}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraTab;
using AlarmAnalysis.Analysis;

namespace AlarmAnalysis
{
    /// <summary>
    /// 高级分析控件
    /// 显示长期持续报警和陈旧报警等高级分析结果
    /// </summary>
    public partial class AdvancedAnalysisControl : DevExpress.XtraEditors.XtraUserControl
    {
        #region 私有字段

        private List<LongStandingAlarmResult> _longStandingAlarms;
        private List<StaleAlarmResult> _staleAlarms;

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取当前活动的GridControl实例（用于导出功能）
        /// </summary>
        public DevExpress.XtraGrid.GridControl GridControl
        {
            get
            {
                if (tabControl.SelectedTabPage == tabPageLongStanding)
                    return gridControlLongStanding;
                else if (tabControl.SelectedTabPage == tabPageStale)
                    return gridControlStale;
                return null;
            }
        }

        /// <summary>
        /// 获取当前显示的数据类型名称
        /// </summary>
        public string CurrentDataTypeName
        {
            get
            {
                if (tabControl.SelectedTabPage == tabPageLongStanding)
                    return "长期持续报警";
                else if (tabControl.SelectedTabPage == tabPageStale)
                    return "陈旧报警";
                return "高级分析";
            }
        }

        #endregion

        #region 构造函数

        public AdvancedAnalysisControl()
        {
            InitializeComponent();
            InitializeControl();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControl()
        {
            try
            {
                // 配置GridView
                ConfigureLongStandingGridView();
                ConfigureStaleGridView();
                
                // 初始化数据
                ClearData();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"初始化高级分析控件时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 配置长期持续报警GridView
        /// </summary>
        private void ConfigureLongStandingGridView()
        {
            // 基本设置
            gridViewLongStanding.OptionsView.ShowGroupPanel = false;
            gridViewLongStanding.OptionsView.ColumnAutoWidth = false;
            gridViewLongStanding.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridViewLongStanding.OptionsSelection.EnableAppearanceFocusedRow = true;
            
            // 禁用编辑
            gridViewLongStanding.OptionsBehavior.Editable = false;
            gridViewLongStanding.OptionsBehavior.ReadOnly = true;
            
            // 配置列
            ConfigureLongStandingColumns();
        }

        /// <summary>
        /// 配置陈旧报警GridView
        /// </summary>
        private void ConfigureStaleGridView()
        {
            // 基本设置
            gridViewStale.OptionsView.ShowGroupPanel = false;
            gridViewStale.OptionsView.ColumnAutoWidth = false;
            gridViewStale.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridViewStale.OptionsSelection.EnableAppearanceFocusedRow = true;
            
            // 禁用编辑
            gridViewStale.OptionsBehavior.Editable = false;
            gridViewStale.OptionsBehavior.ReadOnly = true;
            
            // 配置列
            ConfigureStaleColumns();
        }

        /// <summary>
        /// 配置长期持续报警表格列
        /// </summary>
        private void ConfigureLongStandingColumns()
        {
            gridViewLongStanding.Columns.Clear();
            
            var colSource = gridViewLongStanding.Columns.AddField("SourceName");
            colSource.Caption = "源名称";
            colSource.Width = 150;
            
            var colStation = gridViewLongStanding.Columns.AddField("Station");
            colStation.Caption = "站点";
            colStation.Width = 100;
            
            var colDevice = gridViewLongStanding.Columns.AddField("Device");
            colDevice.Caption = "设备";
            colDevice.Width = 120;
            
            var colMessage = gridViewLongStanding.Columns.AddField("EventMessage");
            colMessage.Caption = "报警消息";
            colMessage.Width = 200;
            
            var colState = gridViewLongStanding.Columns.AddField("CurrentState");
            colState.Caption = "当前状态";
            colState.Width = 80;
            
            var colFirstOccurrence = gridViewLongStanding.Columns.AddField("FirstOccurrence");
            colFirstOccurrence.Caption = "首次发生";
            colFirstOccurrence.Width = 140;
            colFirstOccurrence.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colFirstOccurrence.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            
            var colDuration = gridViewLongStanding.Columns.AddField("DurationText");
            colDuration.Caption = "持续时间";
            colDuration.Width = 100;
            
            var colCount = gridViewLongStanding.Columns.AddField("OccurrenceCount");
            colCount.Caption = "发生次数";
            colCount.Width = 80;
            colCount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            colCount.DisplayFormat.FormatString = "N0";
            
            var colPriority = gridViewLongStanding.Columns.AddField("Priority");
            colPriority.Caption = "优先级";
            colPriority.Width = 60;
        }

        /// <summary>
        /// 配置陈旧报警表格列
        /// </summary>
        private void ConfigureStaleColumns()
        {
            gridViewStale.Columns.Clear();
            
            var colSource = gridViewStale.Columns.AddField("SourceName");
            colSource.Caption = "源名称";
            colSource.Width = 150;
            
            var colStation = gridViewStale.Columns.AddField("Station");
            colStation.Caption = "站点";
            colStation.Width = 100;
            
            var colDevice = gridViewStale.Columns.AddField("Device");
            colDevice.Caption = "设备";
            colDevice.Width = 120;
            
            var colMessage = gridViewStale.Columns.AddField("EventMessage");
            colMessage.Caption = "报警消息";
            colMessage.Width = 200;
            
            var colState = gridViewStale.Columns.AddField("CurrentState");
            colState.Caption = "当前状态";
            colState.Width = 80;
            
            var colLastUpdate = gridViewStale.Columns.AddField("LastUpdate");
            colLastUpdate.Caption = "最后更新";
            colLastUpdate.Width = 140;
            colLastUpdate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colLastUpdate.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            
            var colStaleDuration = gridViewStale.Columns.AddField("StaleDurationText");
            colStaleDuration.Caption = "陈旧时间";
            colStaleDuration.Width = 100;
            
            var colTransitions = gridViewStale.Columns.AddField("StateTransitions");
            colTransitions.Caption = "状态变化";
            colTransitions.Width = 80;
            colTransitions.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            colTransitions.DisplayFormat.FormatString = "N0";
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新分析数据
        /// </summary>
        /// <param name="longStandingAlarms">长期持续报警数据</param>
        /// <param name="staleAlarms">陈旧报警数据</param>
        public void UpdateData(List<LongStandingAlarmResult> longStandingAlarms, 
                              List<StaleAlarmResult> staleAlarms)
        {
            try
            {
                _longStandingAlarms = longStandingAlarms ?? new List<LongStandingAlarmResult>();
                _staleAlarms = staleAlarms ?? new List<StaleAlarmResult>();
                
                // 更新显示
                UpdateLongStandingDisplay();
                UpdateStaleDisplay();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"更新高级分析数据时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空数据
        /// </summary>
        public void ClearData()
        {
            try
            {
                _longStandingAlarms = null;
                _staleAlarms = null;
                
                gridControlLongStanding.DataSource = null;
                gridControlStale.DataSource = null;
                
                gridViewLongStanding.RefreshData();
                gridViewStale.RefreshData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清空高级分析数据时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新长期持续报警显示
        /// </summary>
        private void UpdateLongStandingDisplay()
        {
            if (_longStandingAlarms == null || !_longStandingAlarms.Any())
            {
                gridControlLongStanding.DataSource = null;
                return;
            }

            // 创建显示数据
            var displayData = _longStandingAlarms.Select(alarm => new
            {
                alarm.SourceName,
                alarm.Station,
                alarm.Device,
                alarm.EventMessage,
                alarm.CurrentState,
                alarm.FirstOccurrence,
                DurationText = FormatTimeSpan(alarm.Duration),
                alarm.OccurrenceCount,
                alarm.Priority
            }).ToList();

            gridControlLongStanding.DataSource = displayData;
            gridViewLongStanding.BestFitColumns();
        }

        /// <summary>
        /// 更新陈旧报警显示
        /// </summary>
        private void UpdateStaleDisplay()
        {
            if (_staleAlarms == null || !_staleAlarms.Any())
            {
                gridControlStale.DataSource = null;
                return;
            }

            // 创建显示数据
            var displayData = _staleAlarms.Select(alarm => new
            {
                alarm.SourceName,
                alarm.Station,
                alarm.Device,
                alarm.EventMessage,
                alarm.CurrentState,
                alarm.LastUpdate,
                StaleDurationText = FormatTimeSpan(alarm.StaleDuration),
                alarm.StateTransitions
            }).ToList();

            gridControlStale.DataSource = displayData;
            gridViewStale.BestFitColumns();
        }

        /// <summary>
        /// 格式化时间跨度
        /// </summary>
        /// <param name="timeSpan">时间跨度</param>
        /// <returns>格式化字符串</returns>
        private string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
                return $"{timeSpan.TotalDays:F1}天";
            else if (timeSpan.TotalHours >= 1)
                return $"{timeSpan.TotalHours:F1}小时";
            else if (timeSpan.TotalMinutes >= 1)
                return $"{timeSpan.TotalMinutes:F1}分钟";
            else
                return $"{timeSpan.TotalSeconds:F1}秒";
        }

        #endregion
    }
}

// {{END_MODIFICATIONS}}
