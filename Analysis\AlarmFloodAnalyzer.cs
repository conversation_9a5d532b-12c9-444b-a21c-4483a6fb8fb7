using System;
using System.Collections.Generic;
using System.Linq;
using log4net;
using AlarmAnalysis.Models;
using AlarmAnalysis.Common;

namespace AlarmAnalysis.Analysis
{
    /// <summary>
    /// 报警风暴分析器 - Phase 4功能实现
    /// 使用滑动窗口算法检测报警风暴事件
    /// </summary>
    public class AlarmFloodAnalyzer : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(AlarmFloodAnalyzer));
        
        private readonly int _floodTimeWindowMinutes;
        private readonly int _floodThreshold;
        private bool _disposed = false;
        
        #region 构造函数
        
        /// <summary>
        /// 默认构造函数，从配置文件读取参数
        /// </summary>
        public AlarmFloodAnalyzer()
        {
            _floodTimeWindowMinutes = ConfigurationHelper.GetIntValue("FloodTimeWindowMinutes", 10);
            _floodThreshold = ConfigurationHelper.GetIntValue("FloodThreshold", 10);
            
            _logger.Info($"AlarmFloodAnalyzer初始化完成 - 风暴检测窗口: {_floodTimeWindowMinutes}分钟, 阈值: {_floodThreshold}个");
        }
        
        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="floodTimeWindowMinutes">风暴检测时间窗口（分钟）</param>
        /// <param name="floodThreshold">风暴检测阈值</param>
        public AlarmFloodAnalyzer(int floodTimeWindowMinutes, int floodThreshold)
        {
            _floodTimeWindowMinutes = floodTimeWindowMinutes > 0 ? floodTimeWindowMinutes : 10;
            _floodThreshold = floodThreshold > 0 ? floodThreshold : 10;
            
            _logger.Info($"AlarmFloodAnalyzer初始化完成 - 风暴检测窗口: {_floodTimeWindowMinutes}分钟, 阈值: {_floodThreshold}个");
        }
        
        #endregion
        
        #region 报警风暴检测
        
        /// <summary>
        /// 检测报警风暴事件
        /// </summary>
        /// <param name="alarmEvents">报警事件列表</param>
        /// <returns>报警风暴结果列表</returns>
        public List<AlarmFloodResult> DetectAlarmFloods(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || alarmEvents.Count == 0)
            {
                _logger.Warn("输入的报警事件列表为空，返回空的风暴检测结果");
                return new List<AlarmFloodResult>();
            }

            _logger.Info($"开始报警风暴检测，处理 {alarmEvents.Count} 个报警事件，时间窗口: {_floodTimeWindowMinutes}分钟，阈值: {_floodThreshold}个");

            try
            {
                var floodResults = new List<AlarmFloodResult>();
                var sortedEvents = alarmEvents.OrderBy(e => e.EventDateTime).ToList();
                var windowSize = TimeSpan.FromMinutes(_floodTimeWindowMinutes);

                // 使用滑动窗口算法检测风暴
                for (int i = 0; i < sortedEvents.Count; i++)
                {
                    var windowStart = sortedEvents[i].EventDateTime;
                    var windowEnd = windowStart.Add(windowSize);

                    // 获取窗口内的所有事件
                    var eventsInWindow = sortedEvents
                        .Where(e => e.EventDateTime >= windowStart && e.EventDateTime < windowEnd)
                        .ToList();

                    // 检查是否达到风暴阈值
                    if (eventsInWindow.Count >= _floodThreshold)
                    {
                        // 检查是否已经记录了重叠的风暴
                        bool alreadyRecorded = floodResults.Any(f => 
                            Math.Abs((f.StartTime - windowStart).TotalMinutes) < _floodTimeWindowMinutes / 2);

                        if (!alreadyRecorded)
                        {
                            var floodResult = CreateFloodResult(eventsInWindow, windowStart, windowEnd);
                            floodResults.Add(floodResult);

                            _logger.Debug($"检测到报警风暴: {windowStart:yyyy-MM-dd HH:mm:ss} - {windowEnd:yyyy-MM-dd HH:mm:ss}, " +
                                        $"报警数量: {eventsInWindow.Count}, 首出报警: {floodResult.FirstOutAlarm?.EventMessage}");
                        }
                    }
                }

                _logger.Info($"报警风暴检测完成，发现 {floodResults.Count} 个风暴事件");
                return floodResults;
            }
            catch (Exception ex)
            {
                _logger.Error($"报警风暴检测过程中发生异常: {ex.Message}", ex);
                throw new AnalysisException($"报警风暴检测失败: {ex.Message}", ex, "FloodDetection", alarmEvents.Count);
            }
        }

        /// <summary>
        /// 创建风暴结果对象
        /// </summary>
        private AlarmFloodResult CreateFloodResult(List<AlarmEvent> eventsInWindow, DateTime windowStart, DateTime windowEnd)
        {
            var actualStart = eventsInWindow.Min(e => e.EventDateTime);
            var actualEnd = eventsInWindow.Max(e => e.EventDateTime);
            var duration = actualEnd - actualStart;

            // 计算峰值报警率（每分钟最大报警数）
            var peakRate = CalculatePeakAlarmRate(eventsInWindow);

            // 确定首出报警（时间最早的报警）
            var firstOutAlarm = eventsInWindow.OrderBy(e => e.EventDateTime).First();

            // 统计涉及的站点和设备
            var affectedStations = eventsInWindow.Select(e => e.Station).Where(s => !string.IsNullOrEmpty(s)).Distinct().ToList();
            var affectedDevices = eventsInWindow.Select(e => e.Device).Where(d => !string.IsNullOrEmpty(d)).Distinct().ToList();

            return new AlarmFloodResult
            {
                StartTime = actualStart,
                EndTime = actualEnd,
                Duration = duration,
                WindowStartTime = windowStart,
                WindowEndTime = windowEnd,
                TotalAlarmCount = eventsInWindow.Count,
                PeakAlarmRate = peakRate,
                FirstOutAlarm = new FloodAlarmInfo
                {
                    EventDateTime = firstOutAlarm.EventDateTime,
                    SourceName = firstOutAlarm.SourceName,
                    EventMessage = firstOutAlarm.EventMessage,
                    Station = firstOutAlarm.Station,
                    Device = firstOutAlarm.Device,
                    Severity = firstOutAlarm.Severity
                },
                AffectedStations = affectedStations,
                AffectedDevices = affectedDevices,
                UniqueAlarmTypes = eventsInWindow.Select(e => e.EventMessage).Distinct().Count(),
                FloodEvents = eventsInWindow.Select(e => new FloodAlarmInfo
                {
                    EventDateTime = e.EventDateTime,
                    SourceName = e.SourceName,
                    EventMessage = e.EventMessage,
                    Station = e.Station,
                    Device = e.Device,
                    Severity = e.Severity
                }).ToList()
            };
        }

        /// <summary>
        /// 计算峰值报警率（每分钟最大报警数）
        /// </summary>
        private double CalculatePeakAlarmRate(List<AlarmEvent> events)
        {
            if (events.Count <= 1) return events.Count;

            var sortedEvents = events.OrderBy(e => e.EventDateTime).ToList();
            var maxRate = 0.0;

            // 使用1分钟滑动窗口计算峰值率
            for (int i = 0; i < sortedEvents.Count; i++)
            {
                var windowStart = sortedEvents[i].EventDateTime;
                var windowEnd = windowStart.AddMinutes(1);

                var eventsInMinute = sortedEvents
                    .Where(e => e.EventDateTime >= windowStart && e.EventDateTime < windowEnd)
                    .Count();

                if (eventsInMinute > maxRate)
                {
                    maxRate = eventsInMinute;
                }
            }

            return maxRate;
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _logger.Debug("AlarmFloodAnalyzer资源已释放");
                }
                _disposed = true;
            }
        }

        #endregion
    }
}
