// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#001"
//   Timestamp: "2025-08-24T18:00:00Z"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "新增时间与行为分析控件，遵循现有FrequencyAnalysisControl的设计模式"
// }}
// {{START_MODIFICATIONS}}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using AlarmAnalysis.Analysis;

namespace AlarmAnalysis
{
    /// <summary>
    /// 时间与行为分析控件
    /// 显示报警率统计、时间分布等时间相关的分析结果
    /// </summary>
    public partial class TimeAnalysisControl : DevExpress.XtraEditors.XtraUserControl
    {
        #region 私有字段

        private AlarmRateResult _alarmRateData;

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取GridControl实例（用于导出功能）
        /// </summary>
        public DevExpress.XtraGrid.GridControl GridControl => gridControl;

        /// <summary>
        /// 获取当前显示的数据类型名称
        /// </summary>
        public string CurrentDataTypeName => "时间与行为分析";

        #endregion

        #region 构造函数

        public TimeAnalysisControl()
        {
            InitializeComponent();
            InitializeControl();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControl()
        {
            try
            {
                // 配置GridView
                ConfigureGridView();
                
                // 初始化数据
                ClearData();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"初始化时间分析控件时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 配置GridView
        /// </summary>
        private void ConfigureGridView()
        {
            // 基本设置
            gridView.OptionsView.ShowGroupPanel = false;
            gridView.OptionsView.ColumnAutoWidth = false;
            gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView.OptionsSelection.EnableAppearanceFocusedRow = true;
            
            // 禁用编辑
            gridView.OptionsBehavior.Editable = false;
            gridView.OptionsBehavior.ReadOnly = true;
            
            // 配置列
            ConfigureGridColumns();
        }

        /// <summary>
        /// 配置表格列
        /// </summary>
        private void ConfigureGridColumns()
        {
            // 清空现有列
            gridView.Columns.Clear();
            
            // 添加列
            var colMetric = gridView.Columns.AddField("Metric");
            colMetric.Caption = "统计指标";
            colMetric.Width = 200;
            colMetric.OptionsColumn.AllowEdit = false;
            
            var colValue = gridView.Columns.AddField("Value");
            colValue.Caption = "数值";
            colValue.Width = 150;
            colValue.OptionsColumn.AllowEdit = false;
            
            var colUnit = gridView.Columns.AddField("Unit");
            colUnit.Caption = "单位";
            colUnit.Width = 100;
            colUnit.OptionsColumn.AllowEdit = false;
            
            var colDescription = gridView.Columns.AddField("Description");
            colDescription.Caption = "说明";
            colDescription.Width = 300;
            colDescription.OptionsColumn.AllowEdit = false;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新分析数据
        /// </summary>
        /// <param name="alarmRateData">报警率分析数据</param>
        public void UpdateData(AlarmRateResult alarmRateData)
        {
            try
            {
                _alarmRateData = alarmRateData;
                
                // 更新显示
                UpdateDisplay();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"更新时间分析数据时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空数据
        /// </summary>
        public void ClearData()
        {
            try
            {
                _alarmRateData = null;
                gridControl.DataSource = null;
                gridView.RefreshData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清空时间分析数据时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新显示
        /// </summary>
        private void UpdateDisplay()
        {
            if (_alarmRateData == null)
            {
                ClearData();
                return;
            }

            // 创建显示数据
            var displayData = CreateDisplayData(_alarmRateData);
            
            // 绑定数据
            gridControl.DataSource = displayData;
            
            // 自动调整列宽
            gridView.BestFitColumns();
        }

        /// <summary>
        /// 创建显示数据
        /// </summary>
        /// <param name="data">报警率数据</param>
        /// <returns>显示数据列表</returns>
        private List<object> CreateDisplayData(AlarmRateResult data)
        {
            var displayData = new List<object>();

            // 基础统计
            displayData.Add(new
            {
                Metric = "总报警数",
                Value = data.TotalAlarms.ToString("N0"),
                Unit = "次",
                Description = "分析时间范围内的报警总数"
            });

            displayData.Add(new
            {
                Metric = "分析时间跨度",
                Value = FormatTimeSpan(data.TimeSpan),
                Unit = "",
                Description = "数据的实际时间跨度"
            });

            displayData.Add(new
            {
                Metric = "平均报警率（小时）",
                Value = data.AlarmsPerHour.ToString("F2"),
                Unit = "次/小时",
                Description = "每小时平均报警次数"
            });

            displayData.Add(new
            {
                Metric = "平均报警率（天）",
                Value = data.AlarmsPerDay.ToString("F2"),
                Unit = "次/天",
                Description = "每天平均报警次数"
            });

            displayData.Add(new
            {
                Metric = "平均报警间隔",
                Value = FormatTimeSpan(data.AverageInterval),
                Unit = "",
                Description = "相邻报警之间的平均时间间隔"
            });

            // 峰值报警率
            if (data.PeakAlarmRate != null)
            {
                displayData.Add(new
                {
                    Metric = "峰值报警率",
                    Value = $"{data.PeakAlarmRate.Count} 次/{data.PeakAlarmRate.TimeWindow}分钟",
                    Unit = "",
                    Description = $"峰值时间: {data.PeakAlarmRate.StartTime:yyyy-MM-dd HH:mm:ss}"
                });
            }

            return displayData;
        }

        /// <summary>
        /// 格式化时间跨度
        /// </summary>
        /// <param name="timeSpan">时间跨度</param>
        /// <returns>格式化字符串</returns>
        private string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
                return $"{timeSpan.TotalDays:F1} 天";
            else if (timeSpan.TotalHours >= 1)
                return $"{timeSpan.TotalHours:F1} 小时";
            else if (timeSpan.TotalMinutes >= 1)
                return $"{timeSpan.TotalMinutes:F1} 分钟";
            else
                return $"{timeSpan.TotalSeconds:F1} 秒";
        }

        #endregion
    }
}

// {{END_MODIFICATIONS}}
