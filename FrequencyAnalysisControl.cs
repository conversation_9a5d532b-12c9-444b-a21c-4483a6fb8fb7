// {{RIPER-5:
//   Action: "Added"
//   Task_ID: "#tKXWvU7NTtjp2EwCFP2686"
//   Timestamp: "2025-08-24T16:12:36+08:00"
//   Authoring_Role: "LD"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "频率分析控件实现，包含数据绑定和图表更新功能。"
// }}

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AlarmAnalysis.Analysis;

namespace AlarmAnalysis
{
    /// <summary>
    /// 频率分析控件
    /// </summary>
    public partial class FrequencyAnalysisControl : DevExpress.XtraEditors.XtraUserControl
    {
        #region 私有字段

        private List<AlarmFrequencyResult> _topAlarmMessages;
        private List<AlarmFrequencyResult> _topAlarmingDevices;
        private List<AlarmFrequencyResult> _topAlarmingStations;

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取GridControl实例（用于导出功能）
        /// </summary>
        public DevExpress.XtraGrid.GridControl GridControl => gridControl;

        /// <summary>
        /// 获取当前显示的数据类型名称
        /// </summary>
        public string CurrentDataTypeName
        {
            get
            {
                switch (radioGroup.SelectedIndex)
                {
                    case 0: return "报警消息频率";
                    case 1: return "设备报警频率";
                    case 2: return "站点报警频率";
                    default: return "频率分析";
                }
            }
        }

        #endregion
        
        #region 构造函数
        
        public FrequencyAnalysisControl()
        {
            InitializeComponent();
            InitializeChart();
            InitializeLayout();

            // 设置默认选择
            radioGroup.SelectedIndex = 0;
        }

        /// <summary>
        /// 初始化布局
        /// </summary>
        private void InitializeLayout()
        {
            // 设置分割容器的比例
            this.Load += (s, e) =>
            {
                // 窗口加载后调整分割位置为50%
                splitContainerControl.SplitterPosition = splitContainerControl.Width / 2;
            };

            // 监听窗口大小变化
            this.Resize += FrequencyAnalysisControl_Resize;
        }

        /// <summary>
        /// 窗口大小变化事件处理
        /// </summary>
        private void FrequencyAnalysisControl_Resize(object sender, EventArgs e)
        {
            // 保持分割容器的比例
            if (splitContainerControl.Width > 600)
            {
                splitContainerControl.SplitterPosition = splitContainerControl.Width / 2;
            }

            // 调整表格列宽
            if (gridView.Columns.Count > 0)
            {
                gridView.BestFitColumns();
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 更新分析数据
        /// </summary>
        /// <param name="topAlarmMessages">Top报警消息</param>
        /// <param name="topAlarmingDevices">Top报警设备</param>
        /// <param name="topAlarmingStations">Top报警站点</param>
        public void UpdateData(List<AlarmFrequencyResult> topAlarmMessages, 
                              List<AlarmFrequencyResult> topAlarmingDevices, 
                              List<AlarmFrequencyResult> topAlarmingStations)
        {
            _topAlarmMessages = topAlarmMessages ?? new List<AlarmFrequencyResult>();
            _topAlarmingDevices = topAlarmingDevices ?? new List<AlarmFrequencyResult>();
            _topAlarmingStations = topAlarmingStations ?? new List<AlarmFrequencyResult>();
            
            // 更新当前显示
            UpdateCurrentDisplay();
        }
        
        /// <summary>
        /// 清空数据
        /// </summary>
        public void ClearData()
        {
            _topAlarmMessages = new List<AlarmFrequencyResult>();
            _topAlarmingDevices = new List<AlarmFrequencyResult>();
            _topAlarmingStations = new List<AlarmFrequencyResult>();

            gridControl.DataSource = null;

            // 清空图表数据
            var dataLabel = chartControl.Controls.OfType<Label>().FirstOrDefault(l => l.Dock == DockStyle.Fill);
            if (dataLabel != null)
            {
                dataLabel.Text = "暂无数据";
                dataLabel.ForeColor = Color.Gray;
            }
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 初始化图表
        /// </summary>
        private void InitializeChart()
        {
            // 设置图表面板样式
            chartControl.BackColor = Color.White;
            chartControl.BorderStyle = BorderStyle.FixedSingle;

            // 添加标题标签
            var titleLabel = new Label
            {
                Text = "频率分析图表",
                Font = new Font("Microsoft YaHei", 12, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 30
            };
            chartControl.Controls.Add(titleLabel);

            // 添加占位符标签
            var placeholderLabel = new Label
            {
                Text = "图表功能需要DevExpress Charts组件\n当前显示为占位符",
                Font = new Font("Microsoft YaHei", 10),
                ForeColor = Color.Gray,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            chartControl.Controls.Add(placeholderLabel);
        }
        
        /// <summary>
        /// 更新当前显示
        /// </summary>
        private void UpdateCurrentDisplay()
        {
            List<AlarmFrequencyResult> currentData = null;
            string chartTitle = "";
            
            switch (radioGroup.SelectedIndex)
            {
                case 0: // 按报警消息
                    currentData = _topAlarmMessages;
                    chartTitle = "Top 报警消息频率";
                    break;
                case 1: // 按设备
                    currentData = _topAlarmingDevices;
                    chartTitle = "Top 报警设备频率";
                    break;
                case 2: // 按站点
                    currentData = _topAlarmingStations;
                    chartTitle = "Top 报警站点频率";
                    break;
            }
            
            // 更新表格数据
            UpdateGridData(currentData);
            
            // 更新图表数据
            UpdateChartData(currentData, chartTitle);
        }
        
        /// <summary>
        /// 更新表格数据
        /// </summary>
        /// <param name="data">数据列表</param>
        private void UpdateGridData(List<AlarmFrequencyResult> data)
        {
            try
            {
                if (data == null || data.Count == 0)
                {
                    gridControl.DataSource = null;
                    gridView.RefreshData();
                    return;
                }

                // 创建显示数据
                var displayData = data.Select((item, index) => new
                {
                    Rank = index + 1,
                    Item = GetDisplayName(item),
                    Count = item.Count
                }).ToList();

                gridControl.DataSource = displayData;
                gridView.RefreshData();

                // 自动调整列宽
                gridView.BestFitColumns();
            }
            catch (Exception ex)
            {
                // 记录错误但不中断UI
                System.Diagnostics.Debug.WriteLine($"更新表格数据时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新图表数据
        /// </summary>
        /// <param name="data">数据列表</param>
        /// <param name="title">图表标题</param>
        private void UpdateChartData(List<AlarmFrequencyResult> data, string title)
        {
            try
            {
                // 更新标题
                var titleLabel = chartControl.Controls.OfType<Label>().FirstOrDefault(l => l.Dock == DockStyle.Top);
                if (titleLabel != null)
                {
                    titleLabel.Text = title ?? "频率分析图表";
                }

                // 更新数据显示
                var dataLabel = chartControl.Controls.OfType<Label>().FirstOrDefault(l => l.Dock == DockStyle.Fill);
                if (dataLabel != null)
                {
                    if (data != null && data.Count > 0)
                    {
                        var sb = new StringBuilder();
                        sb.AppendLine("Top 10 数据:");
                        sb.AppendLine();

                        var topData = data.Take(10);
                        foreach (var item in topData.Select((value, index) => new { value, index }))
                        {
                            var displayName = GetDisplayName(item.value);
                            if (displayName.Length > 30)
                            {
                                displayName = displayName.Substring(0, 27) + "...";
                            }
                            sb.AppendLine($"{item.index + 1}. {displayName}: {item.value.Count}");
                        }

                        dataLabel.Text = sb.ToString();
                        dataLabel.ForeColor = Color.Black;
                    }
                    else
                    {
                        dataLabel.Text = "暂无数据";
                        dataLabel.ForeColor = Color.Gray;
                    }
                }

                chartControl.Refresh();
            }
            catch (Exception ex)
            {
                // 记录错误但不中断UI
                System.Diagnostics.Debug.WriteLine($"更新图表数据时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取显示名称
        /// </summary>
        /// <param name="item">频率结果项</param>
        /// <returns>显示名称</returns>
        private string GetDisplayName(AlarmFrequencyResult item)
        {
            if (item == null) return "未知项目";

            try
            {
                switch (radioGroup.SelectedIndex)
                {
                    case 0: // 按报警消息
                        return !string.IsNullOrWhiteSpace(item.ItemName) ? item.ItemName : "未知消息";
                    case 1: // 按设备
                        return !string.IsNullOrWhiteSpace(item.ItemName) ? item.ItemName : "未知设备";
                    case 2: // 按站点
                        return !string.IsNullOrWhiteSpace(item.ItemName) ? item.ItemName : "未知站点";
                    default:
                        return item.ToString() ?? "未知项目";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取显示名称时发生错误: {ex.Message}");
                return "错误项目";
            }
        }
        
        #endregion
        
        #region 事件处理程序
        
        /// <summary>
        /// 单选按钮选择变化事件
        /// </summary>
        private void radioGroup_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateCurrentDisplay();
        }
        
        #endregion
    }
}
