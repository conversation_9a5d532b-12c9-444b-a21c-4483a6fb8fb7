# 数据库连接测试指南

## 测试前准备

### 1. 确认数据库环境
- **数据库服务器**：本地 SQL Server (.)
- **数据库名称**：`DaPeng_IOServer`
- **表名称**：`UFUAAuditLogItem`
- **认证方式**：Windows 集成认证

### 2. 检查连接字符串
确认 `App.config` 中的连接字符串正确：
```xml
<connectionStrings>
  <add name="AlarmDatabase" 
       connectionString="Data Source=.;Initial Catalog=DaPeng_IOServer;Integrated Security=True" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 3. 验证表结构
确认 `UFUAAuditLogItem` 表存在且包含以下字段：
- EventId (uniqueidentifier)
- SourceName (nvarchar)
- EventDateTime (datetime)
- EventMessage (nvarchar)
- EventState (nvarchar)
- Severity (numeric)
- UserName (nvarchar)
- EventSequence (numeric)

## 测试步骤

### 1. 启动应用程序
- 运行 `AlarmAnalysis.exe`
- 确认界面正常加载

### 2. 执行 Phase 3 测试
- 点击 "Phase 3 测试" 按钮
- 观察输出信息

### 3. 预期的输出信息

#### 成功连接的情况：
```
=== Phase 3 高级报警分析测试（真实数据库数据）===

正在连接数据库...
查询时间范围: 2025-01-17 10:30:00 到 2025-01-24 10:30:00
数据库连接成功，查询完成
✅ 成功加载 XXX 条真实数据
从数据库加载了 XXX 条真实记录

数据统计: X 个站点, X 个设备, X 种报警类型

🔄 测试 1: 生命周期重构
  ✓ 生成了 X 个生命周期
  ...
```

#### 连接失败的情况：
```
=== Phase 3 高级报警分析测试（真实数据库数据）===

正在连接数据库...
❌ 数据库连接失败: [具体错误信息]
将使用生成的测试数据进行演示...
从数据库加载了 XXX 条真实记录

🔄 测试 1: 生命周期重构
  ✓ 生成了 X 个生命周期
  ...
```

#### 无数据的情况：
```
=== Phase 3 高级报警分析测试（真实数据库数据）===

正在连接数据库...
查询时间范围: 2025-01-17 10:30:00 到 2025-01-24 10:30:00
数据库连接成功，查询完成
⚠️ 警告：数据库中没有找到指定时间范围内的数据
将使用生成的测试数据进行演示...
从数据库加载了 XXX 条真实记录

🔄 测试 1: 生命周期重构
  ✓ 生成了 X 个生命周期
  ...
```

## 常见问题排查

### 1. 数据库连接失败
**可能原因**：
- SQL Server 服务未启动
- 数据库名称不正确
- Windows 认证权限不足
- 网络连接问题

**解决方案**：
- 检查 SQL Server 服务状态
- 验证数据库名称和连接字符串
- 确认当前用户有数据库访问权限
- 测试网络连接

### 2. 表不存在错误
**错误信息**：`Invalid object name 'UFUAAuditLogItem'`

**解决方案**：
- 确认表名拼写正确
- 检查表是否在正确的数据库中
- 验证用户是否有表的访问权限

### 3. 字段映射错误
**可能原因**：
- 表结构与预期不符
- 字段名称或类型不匹配

**解决方案**：
- 对比表结构与 `AlarmEvent` 模型
- 检查字段的数据类型兼容性
- 必要时调整映射逻辑

### 4. 权限不足错误
**错误信息**：`The SELECT permission was denied`

**解决方案**：
- 为当前用户授予表的 SELECT 权限
- 或使用具有足够权限的数据库用户

## 性能测试

### 1. 数据量测试
- 测试不同数据量下的查询性能
- 观察内存使用情况
- 记录查询响应时间

### 2. 时间范围测试
- 测试不同时间范围的查询
- 验证索引的有效性
- 确认查询优化效果

## 验证清单

- [ ] 数据库连接成功
- [ ] 能够查询到真实数据
- [ ] 数据统计信息正确显示
- [ ] 错误处理机制正常工作
- [ ] 数据回退机制有效
- [ ] Phase 3 所有测试通过
- [ ] Phase 4 所有测试通过
- [ ] 性能表现符合预期

## 注意事项

1. **数据安全**：确保在测试环境中进行，避免影响生产数据
2. **性能影响**：大量数据查询可能影响数据库性能
3. **时间范围**：默认查询最近7天数据，可根据需要调整
4. **错误日志**：查看 `Logs/AlarmAnalysis_Error.log` 获取详细错误信息

## 成功标准

测试成功的标准：
- 能够连接到数据库并查询数据
- 错误处理机制正常工作
- 数据回退机制有效
- 所有分析功能正常运行
- 用户界面反馈清晰友好
