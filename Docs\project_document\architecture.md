# 架构设计文档：AlarmAnalysis UI实现

## 1. 总体架构

### 1.1 UI架构层次
```
AlarmAnalysisForm (RibbonForm)
├── RibbonControl
│   ├── 文件标签页 (数据库设置、退出)
│   ├── 主页标签页 (分析周期、执行分析、导出)
│   └── 视图标签页 (显示选项)
├── DocumentManager (TabbedView)
│   ├── 频率分析标签页
│   ├── 时间与行为分析标签页
│   ├── 高级分析标签页
│   └── 趋势分析标签页
└── RibbonStatusBar (状态、进度条)
```

### 1.2 数据流架构
```
UI层 ←→ 控制层 ←→ 服务层 ←→ 数据层
AlarmAnalysisForm ←→ UIController ←→ AlarmAnalysisService ←→ AlarmDataReader
```

## 2. 核心组件设计

### 2.1 主窗口 (AlarmAnalysisForm)
- **基类**: DevExpress.XtraBars.Ribbon.RibbonForm
- **职责**: 主界面容器、事件协调、状态管理
- **关键属性**:
  - DocumentManager: 内容标签页管理
  - 分析服务实例: AlarmAnalysisService
  - UI状态管理: 忙碌/就绪状态

### 2.2 Ribbon设计
#### 文件标签页
- **数据库设置**: 弹出连接配置对话框
- **退出**: 应用程序关闭

#### 主页标签页
- **分析周期组**:
  - 开始时间: BarEditItem + RepositoryItemDateEdit
  - 结束时间: BarEditItem + RepositoryItemDateEdit
- **执行分析组**:
  - 运行分析: BarButtonItem (异步执行)
- **导出组**:
  - 导出CSV: BarButtonItem
  - 导出Excel: BarButtonItem

### 2.3 DocumentManager标签页设计

#### 频率分析标签页
- **布局**: SplitContainerControl (垂直分割)
- **左侧**: RadioGroup选择 + GridControl表格
- **右侧**: ChartControl柱状图
- **数据源**: TopAlarmMessages/TopAlarmingDevices/TopAlarmingStations

#### 时间与行为分析标签页
- **布局**: XtraTabControl子标签页
- **TTA/TTR分布**: ChartControl直方图
- **持续性报警**: GridControl表格
- **抖动/瞬时报警**: GridControl表格

#### 高级分析标签页
- **布局**: XtraTabControl子标签页
- **报警风暴**: GridControl表格
- **序列模式**: GridControl表格

#### 趋势分析标签页
- **布局**: ChartControl折线图
- **功能**: 缩放、平移交互

## 3. 响应式布局设计

### 3.1 布局策略
- **Dock属性**: 充分利用Fill、Top、Bottom等停靠
- **Anchor属性**: 关键控件设置锚点
- **SplitContainer**: 可调整的分割面板
- **TableLayoutPanel**: 复杂布局的网格管理

### 3.2 缩放适配
- **AutoScaleMode**: Font模式，适应DPI变化
- **MinimumSize**: 设置窗口最小尺寸
- **控件比例**: 使用百分比而非固定像素

## 4. 异步处理架构

### 4.1 异步分析流程
```
UI触发 → 禁用控件 → 后台分析 → 更新UI → 启用控件
```

### 4.2 进度反馈
- **状态栏**: 文字状态更新
- **进度条**: 跑马灯动画
- **按钮状态**: 分析期间禁用

## 5. 数据绑定策略

### 5.1 绑定模式
- **GridControl**: 直接绑定List<T>数据源
- **ChartControl**: 绑定相同数据源，自动同步
- **实时更新**: 数据源变更自动反映到UI

### 5.2 数据转换
- **分析结果**: 直接使用现有模型类
- **图表数据**: 必要时创建适配器类
- **导出数据**: 利用GridControl内置导出
