using System;
using System.Collections.Generic;
using System.Linq;
using log4net;
using AlarmAnalysis.Models;
using AlarmAnalysis.Common;

namespace AlarmAnalysis.Analysis
{
    /// <summary>
    /// 序列模式挖掘器 - Phase 4功能实现
    /// 发现报警之间的强关联序列模式
    /// </summary>
    public class SequencePatternMiner : IDisposable
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(SequencePatternMiner));
        
        private readonly int _sequenceTimeWindowSeconds;
        private readonly int _minSupport;
        private readonly double _minConfidence;
        private readonly int _maxPatterns;
        private bool _disposed = false;
        
        #region 构造函数
        
        /// <summary>
        /// 默认构造函数，从配置文件读取参数
        /// </summary>
        public SequencePatternMiner()
        {
            _sequenceTimeWindowSeconds = ConfigurationHelper.GetIntValue("SequenceTimeWindowSeconds", 30);
            _minSupport = ConfigurationHelper.GetIntValue("SequenceMinSupport", 50);
            _minConfidence = ConfigurationHelper.GetDoubleValue("SequenceMinConfidence", 80.0);
            _maxPatterns = ConfigurationHelper.GetIntValue("SequenceMaxPatterns", 100);
            
            _logger.Info($"SequencePatternMiner初始化完成 - 时间窗口: {_sequenceTimeWindowSeconds}秒, " +
                        $"最小支持度: {_minSupport}, 最小置信度: {_minConfidence}%, 最大模式数: {_maxPatterns}");
        }
        
        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="sequenceTimeWindowSeconds">序列时间窗口（秒）</param>
        /// <param name="minSupport">最小支持度</param>
        /// <param name="minConfidence">最小置信度（百分比）</param>
        /// <param name="maxPatterns">最大模式数</param>
        public SequencePatternMiner(int sequenceTimeWindowSeconds, int minSupport, double minConfidence, int maxPatterns)
        {
            _sequenceTimeWindowSeconds = sequenceTimeWindowSeconds > 0 ? sequenceTimeWindowSeconds : 30;
            _minSupport = minSupport > 0 ? minSupport : 50;
            _minConfidence = minConfidence > 0 && minConfidence <= 100 ? minConfidence : 80.0;
            _maxPatterns = maxPatterns > 0 ? maxPatterns : 100;
            
            _logger.Info($"SequencePatternMiner初始化完成 - 时间窗口: {_sequenceTimeWindowSeconds}秒, " +
                        $"最小支持度: {_minSupport}, 最小置信度: {_minConfidence}%, 最大模式数: {_maxPatterns}");
        }
        
        #endregion
        
        #region 序列模式挖掘
        
        /// <summary>
        /// 挖掘序列模式，发现强关联的报警序列
        /// </summary>
        /// <param name="alarmEvents">报警事件列表（应该是经过抖动过滤的数据）</param>
        /// <returns>序列模式结果</returns>
        public SequencePatternResult MineSequencePatterns(List<AlarmEvent> alarmEvents)
        {
            if (alarmEvents == null || alarmEvents.Count == 0)
            {
                _logger.Warn("输入的报警事件列表为空，返回空的序列模式结果");
                return new SequencePatternResult();
            }

            _logger.Info($"开始序列模式挖掘，处理 {alarmEvents.Count} 个报警事件，时间窗口: {_sequenceTimeWindowSeconds}秒");

            try
            {
                var result = new SequencePatternResult
                {
                    AnalysisTime = DateTime.Now,
                    TotalEvents = alarmEvents.Count,
                    TimeWindow = TimeSpan.FromSeconds(_sequenceTimeWindowSeconds),
                    MinSupport = _minSupport,
                    MinConfidence = _minConfidence
                };

                // 按时间排序
                var sortedEvents = alarmEvents.OrderBy(e => e.EventDateTime).ToList();

                // 生成候选序列对
                var candidatePairs = GenerateCandidatePairs(sortedEvents);
                _logger.Debug($"生成了 {candidatePairs.Count} 个候选序列对");

                // 计算支持度和置信度
                var associationRules = CalculateAssociationRules(candidatePairs, sortedEvents);
                _logger.Debug($"计算了 {associationRules.Count} 个关联规则");

                // 过滤强关联规则
                var strongRules = associationRules
                    .Where(r => r.Support >= _minSupport && r.Confidence >= _minConfidence)
                    .OrderByDescending(r => r.Confidence)
                    .ThenByDescending(r => r.Support)
                    .Take(_maxPatterns)
                    .ToList();

                result.AssociationRules = strongRules;
                result.TotalCandidatePairs = candidatePairs.Count;
                result.StrongRulesCount = strongRules.Count;

                _logger.Info($"序列模式挖掘完成，发现 {strongRules.Count} 个强关联规则");
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error($"序列模式挖掘过程中发生异常: {ex.Message}", ex);
                throw new AnalysisException($"序列模式挖掘失败: {ex.Message}", ex, "SequencePatternMining", alarmEvents.Count);
            }
        }

        /// <summary>
        /// 生成候选序列对
        /// </summary>
        private List<SequencePair> GenerateCandidatePairs(List<AlarmEvent> sortedEvents)
        {
            var pairs = new List<SequencePair>();
            var timeWindow = TimeSpan.FromSeconds(_sequenceTimeWindowSeconds);

            for (int i = 0; i < sortedEvents.Count - 1; i++)
            {
                var eventA = sortedEvents[i];
                var windowEnd = eventA.EventDateTime.Add(timeWindow);

                // 查找时间窗口内的后续事件
                for (int j = i + 1; j < sortedEvents.Count; j++)
                {
                    var eventB = sortedEvents[j];
                    
                    if (eventB.EventDateTime > windowEnd)
                        break; // 超出时间窗口

                    // 避免自身关联（相同的报警消息）
                    if (eventA.EventMessage == eventB.EventMessage)
                        continue;

                    var pair = new SequencePair
                    {
                        AntecedentMessage = eventA.EventMessage,
                        ConsequentMessage = eventB.EventMessage,
                        AntecedentStation = eventA.Station,
                        ConsequentStation = eventB.Station,
                        AntecedentDevice = eventA.Device,
                        ConsequentDevice = eventB.Device,
                        TimeDifference = eventB.EventDateTime - eventA.EventDateTime
                    };

                    pairs.Add(pair);
                }
            }

            return pairs;
        }

        /// <summary>
        /// 计算关联规则的支持度和置信度
        /// </summary>
        private List<AssociationRule> CalculateAssociationRules(List<SequencePair> candidatePairs, List<AlarmEvent> allEvents)
        {
            var rules = new List<AssociationRule>();

            // 按前件和后件分组
            var groupedPairs = candidatePairs
                .GroupBy(p => new { p.AntecedentMessage, p.ConsequentMessage })
                .ToList();

            foreach (var group in groupedPairs)
            {
                var antecedent = group.Key.AntecedentMessage;
                var consequent = group.Key.ConsequentMessage;

                // 计算支持度（A -> B 的发生次数）
                var support = group.Count();

                // 计算前件A的总出现次数
                var antecedentCount = allEvents.Count(e => e.EventMessage == antecedent);

                // 计算置信度 P(B|A) = Support(A -> B) / Support(A)
                var confidence = antecedentCount > 0 ? (double)support / antecedentCount * 100 : 0;

                // 计算平均时间间隔
                var avgTimeDiff = group.Average(p => p.TimeDifference.TotalSeconds);

                // 统计涉及的站点和设备
                var stationPairs = group.Select(p => $"{p.AntecedentStation} -> {p.ConsequentStation}").Distinct().ToList();
                var devicePairs = group.Select(p => $"{p.AntecedentDevice} -> {p.ConsequentDevice}").Distinct().ToList();

                var rule = new AssociationRule
                {
                    AntecedentMessage = antecedent,
                    ConsequentMessage = consequent,
                    Support = support,
                    Confidence = confidence,
                    AverageTimeDifference = TimeSpan.FromSeconds(avgTimeDiff),
                    StationPairs = stationPairs,
                    DevicePairs = devicePairs,
                    Occurrences = group.ToList()
                };

                rules.Add(rule);
            }

            return rules;
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _logger.Debug("SequencePatternMiner资源已释放");
                }
                _disposed = true;
            }
        }

        #endregion
    }
}
