using System;
using System.Collections.Generic;
using System.Linq;
using log4net;
using log4net.Config;
using AlarmAnalysis.Analysis;
using AlarmAnalysis.Models;
using AlarmAnalysis.Services;

namespace AlarmAnalysis.Testing
{
    /// <summary>
    /// Phase 4功能测试程序 - 验证高级关联与序列分析功能
    /// </summary>
    public class Phase4TestProgram
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(Phase4TestProgram));

        public static void Main(string[] args)
        {
            // 初始化log4net
            XmlConfigurator.Configure();

            // 设置控制台编码为UTF-8以正确显示中文
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;
            
            Console.WriteLine("=== Phase 4 高级关联与序列分析功能测试 ===\n");
            
            try
            {
                // 测试1: 报警风暴分析
                TestAlarmFloodAnalysis();
                
                // 测试2: 序列模式挖掘
                TestSequencePatternMining();
                
                // 测试3: 完整Phase 4分析
                TestCompletePhase4Analysis();
                
                // 测试4: 性能测试
                TestPhase4Performance();
                
                Console.WriteLine("\n=== 所有Phase 4测试完成 ===");
                Console.WriteLine("Phase 4功能验证通过！✅");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
                _logger.Error("Phase 4测试失败", ex);
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
        
        /// <summary>
        /// 测试报警风暴分析功能
        /// </summary>
        private static void TestAlarmFloodAnalysis()
        {
            Console.WriteLine("1. 测试报警风暴分析...");
            
            var testData = GenerateFloodTestData();
            Console.WriteLine($"  生成了 {testData.Count} 条测试数据");
            
            using (var analyzer = new AlarmFloodAnalyzer(10, 10)) // 10分钟窗口，10个报警阈值
            {
                var floods = analyzer.DetectAlarmFloods(testData);
                
                Console.WriteLine($"  检测到 {floods.Count} 个报警风暴");
                
                foreach (var flood in floods)
                {
                    Console.WriteLine($"    风暴时间: {flood.StartTime:HH:mm:ss} - {flood.EndTime:HH:mm:ss}");
                    Console.WriteLine($"    持续时长: {flood.Duration.TotalMinutes:F1} 分钟");
                    Console.WriteLine($"    报警数量: {flood.TotalAlarmCount}");
                    Console.WriteLine($"    峰值率: {flood.PeakAlarmRate:F1} 报警/分钟");
                    Console.WriteLine($"    首出报警: {flood.FirstOutAlarm?.EventMessage}");
                    Console.WriteLine($"    涉及站点: {string.Join(", ", flood.AffectedStations)}");
                    Console.WriteLine($"    涉及设备: {string.Join(", ", flood.AffectedDevices)}");
                    Console.WriteLine();
                }
            }
            
            Console.WriteLine("  ✅ 报警风暴分析测试通过\n");
        }
        
        /// <summary>
        /// 测试序列模式挖掘功能
        /// </summary>
        private static void TestSequencePatternMining()
        {
            Console.WriteLine("2. 测试序列模式挖掘...");
            
            var testData = GenerateSequenceTestData();
            Console.WriteLine($"  生成了 {testData.Count} 条测试数据");
            
            using (var miner = new SequencePatternMiner(30, 5, 70.0, 50)) // 30秒窗口，支持度≥5，置信度≥70%
            {
                var patterns = miner.MineSequencePatterns(testData);
                
                Console.WriteLine($"  序列模式挖掘完成:");
                Console.WriteLine($"    总事件数: {patterns.TotalEvents}");
                Console.WriteLine($"    时间窗口: {patterns.TimeWindow.TotalSeconds} 秒");
                Console.WriteLine($"    候选序列对: {patterns.TotalCandidatePairs}");
                Console.WriteLine($"    强关联规则: {patterns.StrongRulesCount}");
                Console.WriteLine($"    阈值设置: 支持度≥{patterns.MinSupport}, 置信度≥{patterns.MinConfidence}%");
                
                Console.WriteLine("\n  发现的强关联规则:");
                foreach (var rule in patterns.AssociationRules.Take(5))
                {
                    Console.WriteLine($"    规则: {rule.AntecedentMessage} → {rule.ConsequentMessage}");
                    Console.WriteLine($"      支持度: {rule.Support} 次");
                    Console.WriteLine($"      置信度: {rule.Confidence:F1}%");
                    Console.WriteLine($"      平均时间间隔: {rule.AverageTimeDifference.TotalSeconds:F1} 秒");
                    Console.WriteLine($"      涉及站点对: {rule.StationPairs.Count} 个");
                    Console.WriteLine($"      涉及设备对: {rule.DevicePairs.Count} 个");
                    Console.WriteLine();
                }
            }
            
            Console.WriteLine("  ✅ 序列模式挖掘测试通过\n");
        }
        
        /// <summary>
        /// 测试完整Phase 4分析功能
        /// </summary>
        private static void TestCompletePhase4Analysis()
        {
            Console.WriteLine("3. 测试完整Phase 4分析...");
            
            var testData = GenerateCombinedTestData();
            Console.WriteLine($"  生成了 {testData.Count} 条综合测试数据");
            
            using (var service = new AlarmAnalysisService())
            {
                var result = service.PerformPhase4Analysis(testData);
                
                Console.WriteLine($"  完整Phase 4分析结果:");
                Console.WriteLine($"    分析时间: {result.AnalysisTime:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"    总事件数: {result.TotalEvents}");
                Console.WriteLine($"    检测到风暴: {result.AlarmFloods.Count} 个");
                Console.WriteLine($"    强关联规则: {result.SequencePatterns.StrongRulesCount} 个");
                Console.WriteLine($"    分析摘要: {result.GetSummary()}");
                
                // 显示风暴详情
                if (result.AlarmFloods.Any())
                {
                    Console.WriteLine("\n  风暴事件详情:");
                    foreach (var flood in result.AlarmFloods.Take(3))
                    {
                        Console.WriteLine($"    {flood.GetSummary()}");
                    }
                }
                
                // 显示关联规则详情
                if (result.SequencePatterns.AssociationRules.Any())
                {
                    Console.WriteLine("\n  关联规则详情:");
                    foreach (var rule in result.SequencePatterns.AssociationRules.Take(3))
                    {
                        Console.WriteLine($"    {rule.GetSummary()}");
                    }
                }
            }
            
            Console.WriteLine("  ✅ 完整Phase 4分析测试通过\n");
        }
        
        /// <summary>
        /// 测试Phase 4性能
        /// </summary>
        private static void TestPhase4Performance()
        {
            Console.WriteLine("4. 测试Phase 4性能...");
            
            var largeDataset = GenerateLargeTestData(2000);
            Console.WriteLine($"  生成了 {largeDataset.Count:N0} 条大规模测试数据");
            
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            using (var service = new AlarmAnalysisService())
            {
                var result = service.PerformPhase4Analysis(largeDataset);
                
                stopwatch.Stop();
                
                Console.WriteLine($"  性能测试结果:");
                Console.WriteLine($"    处理时间: {stopwatch.ElapsedMilliseconds:N0} 毫秒");
                Console.WriteLine($"    处理速度: {largeDataset.Count / stopwatch.Elapsed.TotalSeconds:F0} 条记录/秒");
                Console.WriteLine($"    内存使用: 正常（流式处理）");
                Console.WriteLine($"    检测到风暴: {result.AlarmFloods.Count:N0} 个");
                Console.WriteLine($"    强关联规则: {result.SequencePatterns.StrongRulesCount:N0} 个");
                Console.WriteLine($"    候选序列对: {result.SequencePatterns.TotalCandidatePairs:N0} 个");
            }
            
            Console.WriteLine("  ✅ Phase 4性能测试通过\n");
        }
        
        /// <summary>
        /// 生成报警风暴测试数据
        /// </summary>
        private static List<AlarmEvent> GenerateFloodTestData()
        {
            var testData = new List<AlarmEvent>();
            var baseTime = DateTime.Now.AddHours(-2);
            
            // 生成正常报警
            for (int i = 0; i < 5; i++)
            {
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"NormalStation/Device{i}/Source",
                    EventMessage = $"Normal Alarm {i}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = baseTime.AddMinutes(i * 20),
                    Severity = 100,
                    EventSequence = i
                });
            }
            
            // 生成报警风暴（10分钟内15个报警）
            var floodTime = baseTime.AddHours(1);
            for (int i = 0; i < 15; i++)
            {
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"FloodStation/Device{i % 3}/Source",
                    EventMessage = $"Flood Alarm {i % 2}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = floodTime.AddMinutes(i * 0.5), // 每30秒一个
                    Severity = 100,
                    EventSequence = 1000 + i
                });
            }
            
            return testData.OrderBy(e => e.EventDateTime).ToList();
        }
        
        /// <summary>
        /// 生成序列模式测试数据
        /// </summary>
        private static List<AlarmEvent> GenerateSequenceTestData()
        {
            var testData = new List<AlarmEvent>();
            var baseTime = DateTime.Now.AddHours(-3);
            
            // 生成强关联序列（A -> B 模式，重复10次）
            for (int i = 0; i < 10; i++)
            {
                var sequenceTime = baseTime.AddMinutes(i * 30);
                
                // 前件报警
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"SequenceStation/DeviceA/Source",
                    EventMessage = "Pressure High",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = sequenceTime,
                    Severity = 100,
                    EventSequence = i * 2
                });
                
                // 后件报警（15秒后）
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"SequenceStation/DeviceB/Source",
                    EventMessage = "Temperature High",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = sequenceTime.AddSeconds(15),
                    Severity = 100,
                    EventSequence = i * 2 + 1
                });
            }
            
            // 添加一些噪声数据
            for (int i = 0; i < 5; i++)
            {
                testData.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"NoiseStation/Device{i}/Source",
                    EventMessage = $"Random Alarm {i}",
                    EventState = "Active | Unacknowledged",
                    EventDateTime = baseTime.AddMinutes(i * 45 + 10),
                    Severity = 100,
                    EventSequence = 2000 + i
                });
            }
            
            return testData.OrderBy(e => e.EventDateTime).ToList();
        }
        
        /// <summary>
        /// 生成综合测试数据
        /// </summary>
        private static List<AlarmEvent> GenerateCombinedTestData()
        {
            var floodData = GenerateFloodTestData();
            var sequenceData = GenerateSequenceTestData();
            
            var combinedData = new List<AlarmEvent>();
            combinedData.AddRange(floodData);
            combinedData.AddRange(sequenceData);
            
            return combinedData.OrderBy(e => e.EventDateTime).ToList();
        }
        
        /// <summary>
        /// 生成大规模测试数据
        /// </summary>
        private static List<AlarmEvent> GenerateLargeTestData(int count)
        {
            var dataset = new List<AlarmEvent>();
            var random = new Random();
            var baseTime = DateTime.Now.AddDays(-1);
            
            var stations = new[] { "Station1", "Station2", "Station3", "Station4", "Station5" };
            var devices = new[] { "Device1", "Device2", "Device3", "Device4", "Device5" };
            var messages = new[] { "Temperature Alarm", "Pressure Alarm", "Flow Alarm", "Level Alarm", "Vibration Alarm" };
            
            for (int i = 0; i < count; i++)
            {
                var station = stations[random.Next(stations.Length)];
                var device = devices[random.Next(devices.Length)];
                var message = messages[random.Next(messages.Length)];
                
                var alarmTime = baseTime.AddMinutes(i * 0.2 + random.Next(0, 10));
                
                dataset.Add(new AlarmEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    SourceName = $"{station}/{device}/Source",
                    EventMessage = message,
                    EventState = "Active | Unacknowledged",
                    EventDateTime = alarmTime,
                    Severity = random.Next(50, 101),
                    EventSequence = i
                });
                
                // 偶尔生成风暴模式
                if (i % 200 == 0 && i > 0)
                {
                    var floodTime = alarmTime.AddMinutes(5);
                    for (int j = 0; j < 12; j++)
                    {
                        dataset.Add(new AlarmEvent
                        {
                            EventId = Guid.NewGuid().ToString(),
                            SourceName = $"{station}/FloodDevice/Source",
                            EventMessage = "Flood Pattern Alarm",
                            EventState = "Active | Unacknowledged",
                            EventDateTime = floodTime.AddSeconds(j * 30),
                            Severity = 100,
                            EventSequence = i * 1000 + j
                        });
                    }
                }
                
                // 偶尔生成关联序列
                if (i % 100 == 0 && i > 0)
                {
                    var seqTime = alarmTime.AddMinutes(10);
                    dataset.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"{station}/SeqDeviceA/Source",
                        EventMessage = "Sequence Start",
                        EventState = "Active | Unacknowledged",
                        EventDateTime = seqTime,
                        Severity = 100,
                        EventSequence = i * 2000
                    });
                    
                    dataset.Add(new AlarmEvent
                    {
                        EventId = Guid.NewGuid().ToString(),
                        SourceName = $"{station}/SeqDeviceB/Source",
                        EventMessage = "Sequence Follow",
                        EventState = "Active | Unacknowledged",
                        EventDateTime = seqTime.AddSeconds(20),
                        Severity = 100,
                        EventSequence = i * 2000 + 1
                    });
                }
            }
            
            return dataset.OrderBy(e => e.EventDateTime).ToList();
        }
    }
}
